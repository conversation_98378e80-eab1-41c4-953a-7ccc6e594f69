import React from 'react';
import { apotekComp, apotekCond, apotekExtended, apotekExtraCond, apotekExtraWide, apotekWide, terminaTest } from '../src/lib/fonts';
import { apotek } from '../src/lib/fonts';
import '../src/styles/globals.css';
import '../src/styles/font.css';
import './preview.css';

export const parameters = {
  actions: { argTypesRegex: '^on[A-Z].*' },
};

export const decorators = [  
  (Story) => (
    <div className="bg-black"
    >
    <div className={`${terminaTest.variable} ${apotek.variable} ${apotekWide.variable} ${apotekExtraWide.variable} ${apotekCond.variable} ${apotekExtraCond.variable} ${apotekComp.variable} ${apotekExtended.variable}`}>
      <Story   />
    </div>
    </div>
  ),
];
