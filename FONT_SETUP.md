# Custom Font Setup - TerminaTest

This project has been configured with custom TerminaTest fonts using Next.js 14's optimized font loading system. **Inter is the default font, and TerminaTest is available for specific components.**

## Font Files

The following font weights are available:

- **Thin** (100) - `TerminaTest-Thin.woff`
- **Extra Light** (200) - `TerminaTest-ExtraLight.woff`
- **Light** (300) - `TerminaTest-Light.woff`
- **Regular** (400) - `TerminaTest-Regular.woff`
- **Medium** (500) - `TerminaTest-Medium.woff`
- **Demi** (600) - `TerminaTest-Demi.woff`
- **Bold** (700) - `TerminaTest-Bold.woff`
- **Heavy** (800) - `TerminaTest-Heavy.woff`
- **Black** (900) - `TerminaTest-Black.woff`

## Setup Files

### 1. Font Configuration (`src/lib/fonts.ts`)

- Uses Next.js 14's `localFont` for optimal performance
- Configures all font weights with proper paths
- Sets up CSS variable `--font-termina-test`

### 2. Tailwind Configuration (`tailwind.config.cjs`)

- Keeps Inter as the default sans-serif font
- Creates custom `font-termina` class for TerminaTest
- Uses CSS variable for dynamic font loading

### 3. Global Styles (`src/styles/globals.css`)

- Includes `@font-face` declarations for fallback
- Keeps Inter as the default body font
- Optimized with `font-display: swap`

### 4. Root Layout (`src/app/layout.tsx`)

- No global font variable applied (Inter remains default)
- TerminaTest available for specific components

## Usage

### Using Tailwind Classes

```tsx
// TerminaTest font weights (only when using font-termina class)
<h1 className="font-termina font-black">Black (900)</h1>
<h2 className="font-termina font-extrabold">Heavy (800)</h2>
<h3 className="font-termina font-bold">Bold (700)</h3>
<h4 className="font-termina font-semibold">Demi (600)</h4>
<h5 className="font-termina font-medium">Medium (500)</h5>
<p className="font-termina font-normal">Regular (400)</p>
<p className="font-termina font-light">Light (300)</p>
<p className="font-termina font-extralight">Extra Light (200)</p>
<p className="font-termina font-thin">Thin (100)</p>

// Custom font family class
<p className="font-termina">TerminaTest font family</p>
```

### Using CSS

```css
/* Using the CSS variable */
.my-text {
  font-family: var(--font-termina-test);
  font-weight: 700;
}

/* Direct font family */
.my-heading {
  font-family: 'TerminaTest', sans-serif;
  font-weight: 900;
}
```

### Using CSS-in-JS

```tsx
const styles = {
  heading: {
    fontFamily: 'var(--font-termina-test)',
    fontWeight: 700,
  },
};
```

## Performance Benefits

1. **Next.js 14 Optimization**: Uses `localFont` for better performance
2. **Font Display Swap**: Prevents layout shift during font loading
3. **CSS Variables**: Enables dynamic font switching
4. **Preloading**: Fonts are optimized and preloaded by Next.js

## Utility Components

### TerminaText Component (`src/components/ui/termina-text.tsx`)

A component that applies TerminaTest fonts to specific text elements:

```tsx
import { TerminaText } from '@/components/ui/termina-text';

// Basic usage
<TerminaText>This uses TerminaTest font</TerminaText>

// With different weights and elements
<TerminaText as="h1" weight="bold" className="text-3xl">
  Bold Heading
</TerminaText>

<TerminaText as="p" weight="medium" className="text-lg">
  Medium weight paragraph
</TerminaText>
```

### TerminaWrapper Component (`src/components/ui/termina-wrapper.tsx`)

A wrapper component that applies TerminaTest fonts to any child components:

```tsx
import { TerminaWrapper } from '@/components/ui/termina-wrapper';

<TerminaWrapper className="p-4 bg-gray-100">
  <h1 className="text-3xl font-bold">All text here uses TerminaTest</h1>
  <p className="text-base">This paragraph also uses TerminaTest</p>
</TerminaWrapper>;
```

## Demo Components

Check out these components for complete demonstrations:

- `src/components/ui/font-demo.tsx` - General font demo
- `src/components/ui/termina-text.tsx` - TerminaText component
- `src/components/ui/termina-wrapper.tsx` - TerminaWrapper component

## Storybook Setup

The fonts are also configured for Storybook:

### Files Updated:

- `.storybook/preview.tsx` - Added font variable and decorator
- `.storybook/main.ts` - Added static directory for font files
- `.storybook/preview.css` - Font face declarations for Storybook
- `src/components/ui/font-demo.stories.tsx` - Demo stories

### Testing in Storybook:

1. Start Storybook: `npm run storybook`
2. Navigate to "UI/Font Demo" stories
3. Verify all font weights are displaying correctly

## Troubleshooting

If fonts don't load:

1. Verify font files are in `public/fonts/` directory
2. Check that paths in `src/lib/fonts.ts` are correct
3. Ensure the font variable is applied to the HTML element
4. Clear browser cache and restart development server

### Storybook-specific issues:

1. Ensure `staticDirs: ['../public']` is in `.storybook/main.ts`
2. Check that `.storybook/preview.css` is imported
3. Verify the font variable decorator is applied in preview.tsx
4. Clear Storybook cache: `npm run storybook -- --no-manager-cache`
