import { HeaderType } from "@/components/layouts/header";
import { createContext, Dispatch, SetStateAction, useContext, useState } from "react";


type HeaderContextType = {
    isLoggedIn: boolean;
    setIsLoggedIn: (isLoggedIn: boolean) => void;
    headerType: HeaderType;
    setHeaderType: Dispatch<SetStateAction<HeaderType>>;
}

const headerContext = createContext<HeaderContextType>({
  isLoggedIn: false,
  setIsLoggedIn: () => {},
  headerType: 'default',
  setHeaderType: () => {},
});

export const HeaderProvider = ({ children }: { children: React.ReactNode }) => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [headerType, setHeaderType] = useState<HeaderType>('default');
  return <headerContext.Provider value={{ isLoggedIn, setIsLoggedIn, headerType, setHeaderType }}>{children}</headerContext.Provider>;
};



export const useHeader = () => {
    const context = useContext(headerContext);
    if (!context) {
        throw new Error('useHeader must be used within a HeaderProvider');
    }
    return context;
}