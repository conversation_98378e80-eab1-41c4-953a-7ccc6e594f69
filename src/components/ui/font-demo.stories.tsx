import type { <PERSON>a, StoryObj } from '@storybook/react';
import { FontDemo } from './font-demo';

const meta: Meta<typeof FontDemo> = {
  title: 'UI/Font Demo',
  component: FontDemo,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: 'A demonstration of all TerminaTest font weights and usage examples.',
      },
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const FontWeights: Story = {
  render: () => (
    <div className="p-8 space-y-4">
      <h1 className="text-4xl font-black">Black (900) - TerminaTest</h1>
      <h2 className="text-3xl font-extrabold">Heavy (800) - TerminaTest</h2>
      <h3 className="text-2xl font-bold">Bold (700) - TerminaTest</h3>
      <h4 className="text-xl font-semibold">Demi (600) - TerminaTest</h4>
      <h5 className="text-lg font-medium">Medium (500) - TerminaTest</h5>
      <p className="text-base font-normal">Regular (400) - TerminaTest</p>
      <p className="text-sm font-light">Light (300) - TerminaTest</p>
      <p className="text-xs font-extralight">Extra Light (200) - TerminaTest</p>
      <p className="text-xs font-thin">Thin (100) - TerminaTest</p>
    </div>
  ),
};

export const CustomFontFamily: Story = {
  render: () => (
    <div className="p-8 space-y-4">
      <h2 className="text-2xl font-bold mb-4">Using Custom Font Family Class</h2>
      <p className="font-termina text-lg">This text uses the custom 'font-termina' class</p>
      <p className="font-termina text-base font-medium">Medium weight with custom font family</p>
      <p className="font-termina text-sm font-light">Light weight with custom font family</p>
    </div>
  ),
}; 