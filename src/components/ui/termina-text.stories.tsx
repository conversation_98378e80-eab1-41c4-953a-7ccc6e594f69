import type { <PERSON>a, StoryObj } from '@storybook/react';
import { TerminaText } from './termina-text';

const meta: Meta<typeof TerminaText> = {
  title: 'UI/TerminaText',
  component: TerminaText,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: 'A component that applies TerminaTest fonts to specific text elements with different weights.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    weight: {
      control: 'select',
      options: ['thin', 'extralight', 'light', 'normal', 'medium', 'semibold', 'bold', 'extrabold', 'black'],
    },
    as: {
      control: 'select',
      options: ['span', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'div'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: 'This text uses TerminaTest font',
  },
};

export const AllWeights: Story = {
  render: () => (
    <div className="space-y-4">
      <TerminaText as="h1" weight="black" className="text-4xl">
        Black (900) - TerminaTest
      </TerminaText>
      <TerminaText as="h2" weight="extrabold" className="text-3xl">
        Extra Bold (800) - TerminaTest
      </TerminaText>
      <TerminaText as="h3" weight="bold" className="text-2xl">
        Bold (700) - TerminaTest
      </TerminaText>
      <TerminaText as="h4" weight="semibold" className="text-xl">
        Semi Bold (600) - TerminaTest
      </TerminaText>
      <TerminaText as="h5" weight="medium" className="text-lg">
        Medium (500) - TerminaTest
      </TerminaText>
      <TerminaText as="p" weight="normal" className="text-base">
        Normal (400) - TerminaTest
      </TerminaText>
      <TerminaText as="p" weight="light" className="text-sm">
        Light (300) - TerminaTest
      </TerminaText>
      <TerminaText as="p" weight="extralight" className="text-xs">
        Extra Light (200) - TerminaTest
      </TerminaText>
      <TerminaText as="p" weight="thin" className="text-xs">
        Thin (100) - TerminaTest
      </TerminaText>
    </div>
  ),
};

export const AsDifferentElements: Story = {
  render: () => (
    <div className="space-y-4">
      <TerminaText as="h1" weight="bold" className="text-3xl">
        This is an H1 heading
      </TerminaText>
      <TerminaText as="h2" weight="semibold" className="text-2xl">
        This is an H2 heading
      </TerminaText>
      <TerminaText as="p" weight="normal" className="text-base">
        This is a paragraph with TerminaTest font.
      </TerminaText>
      <TerminaText as="span" weight="medium" className="text-sm">
        This is a span element
      </TerminaText>
    </div>
  ),
};

export const WithCustomStyling: Story = {
  render: () => (
    <div className="space-y-4">
      <TerminaText
        as="h1"
        weight="black"
        className="text-4xl text-blue-600 bg-blue-50 p-4 rounded-lg"
      >
        Styled Heading
      </TerminaText>
      <TerminaText
        as="p"
        weight="medium"
        className="text-lg text-gray-700 bg-gray-100 p-3 rounded border-l-4 border-green-500"
      >
        Styled paragraph with custom background and border
      </TerminaText>
    </div>
  ),
}; 