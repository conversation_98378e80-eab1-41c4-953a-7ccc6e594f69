import React from 'react';

export const FontDemo: React.FC = () => {
  return (
    <div className="p-8 space-y-6">
      <h1 className="text-4xl font-black">TerminaTest Black (900)</h1>
      <h2 className="text-3xl font-extrabold">TerminaTest Heavy (800)</h2>
      <h3 className="text-2xl font-bold">TerminaTest Bold (700)</h3>
      <h4 className="text-xl font-semibold">TerminaTest Demi (600)</h4>
      <h5 className="text-lg font-medium">TerminaTest Medium (500)</h5>
      <p className="text-base font-normal">TerminaTest Regular (400)</p>
      <p className="text-sm font-light">TerminaTest Light (300)</p>
      <p className="text-xs font-extralight">TerminaTest Extra Light (200)</p>
      <p className="text-xs font-thin">TerminaTest Thin (100)</p>
      
      <div className="mt-8 p-4 bg-gray-100 rounded">
        <h3 className="text-lg font-bold mb-2">Using Tailwind Classes:</h3>
        <ul className="space-y-1 text-sm">
          <li><code>font-thin</code> - TerminaTest Thin (100)</li>
          <li><code>font-extralight</code> - TerminaTest Extra Light (200)</li>
          <li><code>font-light</code> - TerminaTest Light (300)</li>
          <li><code>font-normal</code> - TerminaTest Regular (400)</li>
          <li><code>font-medium</code> - TerminaTest Medium (500)</li>
          <li><code>font-semibold</code> - TerminaTest Demi (600)</li>
          <li><code>font-bold</code> - TerminaTest Bold (700)</li>
          <li><code>font-extrabold</code> - TerminaTest Heavy (800)</li>
          <li><code>font-black</code> - TerminaTest Black (900)</li>
        </ul>
      </div>
      
      <div className="mt-8 p-4 bg-blue-50 rounded">
        <h3 className="text-lg font-bold mb-2">Using Custom Font Family:</h3>
        <p className="font-termina text-lg">This uses the custom 'termina' font family class</p>
      </div>
    </div>
  );
}; 