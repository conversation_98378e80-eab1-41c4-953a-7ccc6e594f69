import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { TerminaWrapper } from './termina-wrapper';

const meta: Meta<typeof TerminaWrapper> = {
  title: 'UI/TerminaWrapper',
  component: TerminaWrapper,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: 'A wrapper component that applies TerminaTest fonts to any child components.',
      },
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: 'This text uses TerminaTest font',
  },
};

export const WithMultipleElements: Story = {
  render: () => (
    <TerminaWrapper className="space-y-4">
      <h1 className="text-4xl font-black">Black Heading</h1>
      <h2 className="text-3xl font-bold">Bold Subheading</h2>
      <p className="text-base font-normal">
        This paragraph and all text within this wrapper will use TerminaTest font.
      </p>
      <div className="text-sm font-light">
        Even nested elements inherit the font family.
      </div>
    </TerminaWrapper>
  ),
};

export const WithCustomStyling: Story = {
  render: () => (
    <TerminaWrapper className="p-6 bg-blue-50 rounded-lg border border-blue-200">
      <h1 className="text-3xl font-black text-blue-800 mb-4">
        Styled Wrapper
      </h1>
      <p className="text-lg font-medium text-blue-700 mb-2">
        This wrapper has custom styling applied.
      </p>
      <p className="text-base font-normal text-blue-600">
        All text within this wrapper uses TerminaTest font while maintaining the custom styling.
      </p>
    </TerminaWrapper>
  ),
};

export const MixedContent: Story = {
  render: () => (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">Default Inter Font</h2>
        <p className="text-base">
          This text uses the default Inter font family.
        </p>
      </div>
      
      <TerminaWrapper className="p-4 bg-gray-100 rounded">
        <h2 className="text-2xl font-bold mb-2">TerminaTest Font</h2>
        <p className="text-base">
          This text uses TerminaTest font family.
        </p>
        <ul className="mt-2 space-y-1">
          <li className="font-medium">• Medium weight</li>
          <li className="font-light">• Light weight</li>
          <li className="font-bold">• Bold weight</li>
        </ul>
      </TerminaWrapper>
      
      <div>
        <h2 className="text-2xl font-bold mb-2">Back to Inter Font</h2>
        <p className="text-base">
          This text uses the default Inter font family again.
        </p>
      </div>
    </div>
  ),
}; 