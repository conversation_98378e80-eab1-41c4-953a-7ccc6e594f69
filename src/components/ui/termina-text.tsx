import React from 'react';
import { cn } from '@/utils/cn';

interface TerminaTextProps {
  children: React.ReactNode;
  className?: string;
  weight?: 'thin' | 'extralight' | 'light' | 'normal' | 'medium' | 'semibold' | 'bold' | 'extrabold' | 'black';
  as?: keyof JSX.IntrinsicElements;
}

export const TerminaText: React.FC<TerminaTextProps> = ({
  children,
  className,
  weight = 'normal',
  as: Component = 'span',
  ...props
}) => {
  const weightClasses = {
    thin: 'font-thin',
    extralight: 'font-extralight',
    light: 'font-light',
    normal: 'font-normal',
    medium: 'font-medium',
    semibold: 'font-semibold',
    bold: 'font-bold',
    extrabold: 'font-extrabold',
    black: 'font-black',
  };

  return (
    <Component
      className={cn(
        'font-termina',
        weightClasses[weight],
        className
      )}
      {...props}
    >
      {children}
    </Component>
  );
}; 