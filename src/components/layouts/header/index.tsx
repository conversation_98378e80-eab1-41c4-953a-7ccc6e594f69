'use client';
import Image from 'next/image';
import { PUBLIC_IMAGES } from '@/lib/constants/publicImages';
import {
  DEFAULT_HEADER_MENUS,
  HeaderMenu,
  SMART_INFO_HEADER_MENUS,
  SMART_ODDS_HEADER_MENUS,
  SMART_PLAY_HEADER_MENUS,
  SMART_TIPSTER_HEADER_MENUS,
} from '@/lib/constants/headerMenu';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import {
  SmartArrowDown,
  SmartArrowRight,
  SmartArrowUp,
} from '@/components/Icons/SmartArrow';
import { cn } from '@/lib/utils';
import { useHeader } from '@/store/header';
import { useRouter } from 'next/navigation';
import { AlignJustify } from 'lucide-react';

export type HeaderType =
  | 'default'
  | 'smartPlay'
  | 'smartOdds'
  | 'smartTipping'
  | 'smartInfo';

type HeaderProps = {
  isLoggedIn?: boolean;
};


const Header = ({ isLoggedIn = false }: HeaderProps) => {
  const { headerType, setHeaderType } = useHeader();
  const router = useRouter();


  let menus: HeaderMenu[] = [];
  switch (headerType) {
    case 'smartPlay':
      menus = SMART_PLAY_HEADER_MENUS;
      break;
    case 'smartInfo':
      menus = SMART_INFO_HEADER_MENUS;
      break;
    case 'smartOdds':
      menus = SMART_ODDS_HEADER_MENUS;
      break;
    case 'smartTipping':
      menus = SMART_TIPSTER_HEADER_MENUS;
      break;
    default:
      menus = DEFAULT_HEADER_MENUS;
  }

  return (
    <div className="bg-white min-h-[64px] z-[1200] px-4 w-full flex justify-between items-center shadow-md border border-gray-50">
      <div className="flex items-center gap-4 min-h-[64px]">
        <div className="lg:hidden flex items-center justify-center cursor-pointer" onClick={()=>{
          setHeaderType('default');
          router.push('/');
        }}>
        <AlignJustify className='text-textprimary' />
        </div>
        <Image
          src={PUBLIC_IMAGES.SMARTBLOGO}
          alt="SmartB Logo"
          width={100}
          height={100}
          onClick={()=>{
            setHeaderType('default');
            router.push('/');
          }}
          className="cursor-pointer"
        />
      </div>
      <div className={cn('lg:flex hidden z-[1200] items-center font-bold text-textprimary gap-4')}>
        {menus.map((menu) => {
          const submenuToRender = menu.subMenus?.map((subMenu) => (
            <div
              key={subMenu.href}

              className="submenu-item relative flex items-center space-x-2 p-2 hover:bg-gray-100 transition-colors rounded-md"
            >
              {subMenu.logo && <subMenu.logo />}
              <Link href={subMenu.href} className="flex-1">
                {subMenu.label}
              </Link>

              <div className="opacity-0 [.submenu-item:hover_&]:opacity-100 transition-opacity">
                <SmartArrowRight />
              </div>
            </div>
          ));
          const MenuItemToRender = menu.logo ? (
            <div className="min-h-[64px] flex items-center justify-center"

            >
              <div
                onClick={()=>{
                  if(menu.type){
                    setHeaderType(menu.type);
                  }
                }}
                className="hover:bg-gray-100 p-2 rounded-md hover:text-primary-200 cursor-pointer"
              >
                <Image
                  src={menu.logo}
                  alt={menu.label}
                  width={100}
                  height={30}
                />
              </div>
            </div>
          ) : (
            <div className="relative group">
              <div className="flex items-center gap-2 hover:bg-gray-100 px-2 rounded-md hover:text-primary-200">
                <Link href={menu.href} className="py-2">
                  {menu.label}
                </Link>
                {menu.subMenus && (
                  <div className="flex items-center gap-2">
                    <div className="group-hover:opacity-0 transition-opacity">
                      <SmartArrowDown />
                    </div>
                    <div className="absolute opacity-0 group-hover:opacity-100 transition-opacity">
                      <SmartArrowUp />
                    </div>
                  </div>
                )}
              </div>
              {submenuToRender && (
                <div
                  className={cn(
                    'absolute p-4 text-[16px]  font-normal left-0 top-14 bg-white shadow-xl rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200',
                    menu.href.includes('sports')
                      ? 'grid grid-cols-2 h-[394px] w-[597px]'
                      : 'grid grid-cols-1 w-[297px]',
                  )}
                >
                  {submenuToRender}
                </div>
              )}
            </div>
          );

          return (
            <div key={menu.href} className="text-xs sm:text-sm md:text-base">
              {MenuItemToRender}
            </div>
          );
        })}
      </div>

      {isLoggedIn ? (
        <div className="flex items-center gap-4">
          <Image
            src={PUBLIC_IMAGES.DEFAULT_USER_IMAGE}
            alt="SmartB Logo"
            width={36}
            height={36}
            unoptimized={true}
          />
        </div>
      ) : (
        <div className="flex items-center gap-2">
          <Button variant={'primarybutton'}>Sign Up</Button>
          <Button variant={'primarybuttonoutline'}>Login</Button>
        </div>
      )}
    </div>
  );
};

export default Header;
