'use client';
import './headerevent.scss';
import UpcommingCarousel from './UpcommingCarousel';


const HeaderEvents = () => {
  return (
        <div className="bg-jump-gradient h-[130px] w-full relative">
      <div className="flex flex-row h-full">
        <div className="max-w-[152px] max-767:max-w-[42px] h-full w-full py-[31px] px-[27px] border-r-[1px] border-white max-767:px-[7px] content-center">
          <p className="text-white text-base leading-[20px] font-semibold mb-[10px] max-767:hidden">
            Next events
          </p>
          {/* <FilterDropdown /> */}
        </div>
        <div className="w-[calc(100%-152px)] max-767:w-[calc(100%-42px)] h-full relative">
          <UpcommingCarousel
            country={[]}
            filter={[]}
            sport={[4, 12]}
            isLive={false}
          />
        </div>
      </div>
    </div>
  );
};

export default HeaderEvents;
