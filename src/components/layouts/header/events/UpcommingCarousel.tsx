'use client';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';

import moment from 'moment';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';
import Countdown from 'react-countdown';
import type { CustomArrowProps } from 'react-slick';
import Slider from 'react-slick';
import type { Settings } from 'react-slick';

import Loader from '@/components/shared/Loader';
import axiosInstance from '@/lib/axiosInstance';
import { Config } from '@/config/config';

import Ar from '@/assets/images/sports/AR.svg';
import Baseball from '@/assets/images/sports/baseball.svg';
import Basketball from '@/assets/images/sports/basketball.svg';
import Boxing from '@/assets/images/sports/boxing.svg';
import Cricket from '@/assets/images/sports/cricket.svg';
import Horse from '@/assets/images/sports/filter1.svg';
import Football from '@/assets/images/sports/football.svg';

import Golf from '@/assets/images/sports/golf.svg';
import Greys from '@/assets/images/sports/Greyhound.svg';
import Harnes from '@/assets/images/sports/Harness.svg';
import IceHockey from '@/assets/images/sports/iceHockey.svg';
import Mma from '@/assets/images/sports/mma.svg';
import Ru from '@/assets/images/sports/RU.svg';
import Rugby from '@/assets/images/sports/rugby.svg';
import Soccer from '@/assets/images/sports/soccer.svg';
import Tennis from '@/assets/images/sports/Tennis.svg';
import { NextSlideArrow, PrvSlideArrow, WinArrowIcon } from './Icons';
import { useScreen } from '@/hooks/useScreen';

const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

interface Race {
  id: number;
  eventDate: string;
  sportId: number;
  sportName: string;
  trackId: number;
  countryId: number;
}

interface ApiResponse {
  result: Race[];
  status: number;
  data: any;
}

const renderer = ({ days, hours, minutes, seconds, completed }: any) => {
  let formattedTime = '';

  if (days > 0) {
    formattedTime = `${days}d ${hours}h`;
  } else if (hours > 0) {
    formattedTime = `${hours}h ${minutes}m`;
  } else if (minutes > 0) {
    formattedTime = `${minutes}m`;
  } else if (seconds > 0) {
    formattedTime = `${seconds}s`;
  }

  if (completed) {
    // Render a completed state
    return (
      <span className="text-xs leading-[15px] bg-orange-500 text-white font-semibold p-[3px] rounded-[3px]">
        Closed
      </span>
    );
  } else {
    return (
      <span className="text-xs leading-[15px] bg-orange-500 text-white font-semibold p-[3px] rounded-[3px]">
        {formattedTime}
      </span>
    );
  }
};

type ReadonlyCustomArrowProps = Readonly<CustomArrowProps>;

function NextArrow(props: ReadonlyCustomArrowProps) {
  const { className, onClick } = props;
  return (
    <button className={className} onClick={onClick}>
      <NextSlideArrow className="h-7 w-7" />
    </button>
  );
}

function PrevArrow(props: ReadonlyCustomArrowProps) {
  const { className, onClick } = props;
  return (
    <button className={className} onClick={onClick}>
      <PrvSlideArrow className="h-7 w-7" />
    </button>
  );
}

const UpcommingCarousel = ({ country, filter, sport, isLive }: any) => {
  const router = useRouter();
  const carouselRef = useRef<Slider>(null);
  const { width } = useScreen();
  const [sliderData, setSliderData] = useState<Race[]>([]);
  const [isTopBarLoading, setIsTopBarLoading] = useState(true);
  const [sliderIndex, setSliderIndex] = useState(0);
  const [prevData, setPrevData] = useState([]);
  const [nextData, setNextData] = useState([]);
  const [nextSearchDate, setNextSearchDate] = useState('');
  const [pastSearchDate, setPastSearchDate] = useState('');
  const [activeSlide, setActiveSlide] = useState(0);
  const [disableNext, setDisableNext] = useState(false);
  const [disablePrev, setDisablePrev] = useState(false);
  const [nextOffset, setNextOffset] = useState(1);
  const [prevOffset, setPrevOffset] = useState(1);

  useEffect(() => {
    GetUpcomingRace();
  }, [country, filter, sport, isLive]);

  const fetchRaceData = async (urlArray: string[]): Promise<ApiResponse[]> => {
    const results: ApiResponse[] = [];
    await Promise.all(
      urlArray.map(async (url, index) => {
        const response = await axiosInstance.get(Config.baseURL + url);
        // @ts-expect-error
        results[index] = response;
      }),
    );
    return results;
  };

  const calculateSliderIndices = (
    DefaultData: ApiResponse | undefined,
    FirstPrevData: ApiResponse | undefined,
    width: number,
    slidesToShow: number,
  ): { calculatedIndex: number; activeSlide: number } => {
    let calculatedIndex = 0;
    let activeSlide = 0;

    if (DefaultData?.data?.result?.length > 0) {
      calculatedIndex = FirstPrevData?.data?.result?.length || 0;
    } else {
      calculatedIndex =
        width >= 1120
          ? (FirstPrevData?.data?.result?.length || 0) - slidesToShow
          : FirstPrevData?.data?.result?.length || 0;
    }

    activeSlide = calculatedIndex;

    return { calculatedIndex, activeSlide };
  };

  const mergeAndSortData = (
    DefaultData: ApiResponse | undefined,
    FirstPrevData: ApiResponse | undefined,
    isLive: boolean,
  ): any[] => {
    let mergedData = [];
    if (isLive) {
      mergedData = [...(DefaultData?.data?.result || [])];
    } else {
      mergedData = [
        ...(FirstPrevData?.data?.result || []),
        ...(DefaultData?.data?.result || []),
      ];
    }

    return mergedData.toSorted((a: any, b: any) => {
      return (
        new Date(a?.eventDate).getTime() - new Date(b?.eventDate).getTime()
      );
    });
  };

  const GetUpcomingRace = async () => {
    setIsTopBarLoading(true);

    const SelectedSportId = [...filter, ...sport];
    const SelectedState = country;

    const nextJumpSportUrlArray = isLive
      ? [
        `nextJumpSport?sportId=${SelectedSportId}&timezone=${timezone}&type=next&MeetingState=${SelectedState}&status=${isLive ? `inprogress` : ``
        }`,
      ]
      : [
        `nextJumpSport?sportId=${SelectedSportId}&timezone=${timezone}&type=next&MeetingState=${SelectedState}&status=`,
        `nextJumpSport?sportId=${SelectedSportId}&timezone=${timezone}&type=prev&limit=48&MeetingState=${SelectedState}&status=`,
      ];

    try {
      const [DefaultData, FirstPrevData] = await fetchRaceData(
        nextJumpSportUrlArray,
      );
      console.log('Fetched data:', { DefaultData, FirstPrevData });

      const isValidResponse = isLive
        ? DefaultData?.status === 200
        : DefaultData?.status === 200 && FirstPrevData?.status === 200;

      if (isValidResponse) {
        if (!isLive) {
          const { calculatedIndex, activeSlide } = calculateSliderIndices(
            DefaultData,
            FirstPrevData,
            width,
            settings?.slidesToShow,
          );
          setSliderIndex(calculatedIndex);
          setActiveSlide(activeSlide);
        } else {
          setSliderIndex(0);
          setActiveSlide(0);
        }

        const FinalData = mergeAndSortData(DefaultData, FirstPrevData, isLive);

        setDisableNext(
          FinalData.length === 0 || DefaultData?.data?.result?.length < 96,
        );
        setDisablePrev(
          FinalData.length === 0 || FirstPrevData?.data?.result?.length < 48,
        );
        setSliderData(FinalData);
        if (FinalData.length > 0) {
          setNextSearchDate(FinalData.slice(-1)[0].eventDate);
          setPastSearchDate(FinalData[0].eventDate);
        }
      } else {
        // If API fails, set mock data for testing
        console.log('API response invalid, using mock data');
        const mockData = [
          {
            id: 1,
            eventDate: new Date(Date.now() + 3600000).toISOString(), // 1 hour from now
            sportId: 4,
            sportName: 'Cricket',
            trackId: 1,
            countryId: 1,
            homeName: 'Team A',
            awayName: 'Team B',
            tournamentName: 'Test Tournament'
          },
          {
            id: 2,
            eventDate: new Date(Date.now() + 7200000).toISOString(), // 2 hours from now
            sportId: 8,
            sportName: 'Soccer',
            trackId: 2,
            countryId: 1,
            homeName: 'Team C',
            awayName: 'Team D',
            tournamentName: 'Test League'
          }
        ];
        setSliderData(mockData);
        setSliderIndex(0);
        setActiveSlide(0);
        setDisableNext(false);
        setDisablePrev(false);
      }
    } catch (error) {
      console.error('Error fetching upcoming races:', error);
      // Set mock data for testing when API fails
      const mockData = [
        {
          id: 1,
          eventDate: new Date(Date.now() + 3600000).toISOString(),
          sportId: 4,
          sportName: 'Cricket',
          trackId: 1,
          countryId: 1,
          homeName: 'Team A',
          awayName: 'Team B',
          tournamentName: 'Test Tournament'
        }
      ];
      setSliderData(mockData);
      setSliderIndex(0);
      setActiveSlide(0);
      setDisableNext(false);
      setDisablePrev(false);
    } finally {
      setIsTopBarLoading(false);
    }
  };

  const fetchpastResultdata = async (offset: any) => {
    const SelectedSportId = [...filter, ...sport];
    const SelectedState = country;

    try {
      const { status, data } = await axiosInstance.get(
        Config.baseURL +
        `nextJumpSport?sportId=${SelectedSportId}&timezone=${timezone}&type=prev&page=${offset}&limit=48&MeetingState=${SelectedState}&searchDate=${pastSearchDate}&status=${isLive ? `inprogress` : ``
        }`,
      );
      if (status === 200) {
        setPrevOffset(offset);
        setPrevData(data?.result);
        setDisablePrev(data?.result?.length < 48);
      }
    } catch (_err) {
      setIsTopBarLoading(false);
    }
  };

  const fetchNextResultdata = async (offset: any) => {
    const SelectedSportId = [...filter, ...sport];
    const SelectedState = country;
    try {
      const { status, data } = await axiosInstance.get(
        Config.baseURL +
        `nextJumpSport?sportId=${SelectedSportId}&timezone=${timezone}&type=next&page=${offset}&limit=48&MeetingState=${SelectedState}&searchDate=${nextSearchDate}&status=${isLive ? `inprogress` : ``
        }`,
      );
      if (status === 200) {
        setNextOffset(offset);
        setNextData(data?.result);
        setDisableNext(data?.result?.length < 48);
      }
    } catch (_err) {
      setIsTopBarLoading(false);
    }
  };
  const MergePrevData = () => {
    // @ts-expect-error

    setSliderIndex(prevData?.length - carouselRef.current?.props?.slidesToShow);
    // @ts-expect-error

    setActiveSlide(prevData?.length - carouselRef.current?.props?.slidesToShow);
    const mergedData = [...prevData, ...sliderData];
    const FinalData = mergedData?.toSorted(function (a, b) {
      // @ts-expect-error

      return new Date(a?.eventDate) - new Date(b?.eventDate);
    });

    setSliderData(FinalData);
    setNextSearchDate(FinalData.slice(-1)[0].eventDate); // set date for search
    setPastSearchDate(FinalData[0].eventDate); // set date for search
    setPrevData([]); // empty data after merge
    setTimeout(() => {
      setIsTopBarLoading(false);
    }, 500);
  };
  const MergeNextData = () => {
    setSliderIndex(
      // @ts-expect-error

      sliderData?.length - carouselRef.current?.props?.slidesToShow,
    );
    setActiveSlide(
      // @ts-expect-error

      sliderData?.length - carouselRef.current?.props?.slidesToShow,
    );
    const mergedData = [...sliderData, ...nextData];
    const FinalData = mergedData?.toSorted(function (a, b) {
      // @ts-expect-error

      return new Date(a?.eventDate) - new Date(b?.b?.eventDate);
    });

    setSliderData(FinalData);
    setNextSearchDate(FinalData.slice(-1)[0].eventDate); // set date for search
    setPastSearchDate(FinalData[0].eventDate); // set date for search
    setNextData([]); // empty data after merge
    setTimeout(() => {
      setIsTopBarLoading(false);
    }, 500);
  };

  const handleRaceNavigation = (item: any) => {
    if (item?.sportId === 1 || item?.sportId === 2 || item?.sportId === 3) {
      return;
    }

    const getSportName = (sportName: string | undefined): string => {
      const trimmedName = sportName?.trim();
      switch (trimmedName) {
        case 'Cricket':
          return 'cricket';
        case 'Basketball':
          return 'basketball';
        case 'American Football':
          return 'americanfootball';
        case 'Australian Rules':
          return 'australianrules';
        case 'Golf':
          return 'golf';
        case 'Tennis':
          return 'tennis';
        case 'Baseball':
          return 'baseball';
        case 'Ice Hockey':
          return 'icehockey';
        case 'Boxing':
          return 'boxing';
        case 'Mixed Martial Arts':
          return 'mma';
        case 'Soccer':
          return 'soccer';
        case 'Rugby League':
          return 'rugbyleague';
        case 'Rugby Union':
          return 'rugbyunion';
        default:
          return 'rugbyunionsevens';
      }
    };

    const sportName = getSportName(item?.sportName);

    if (Config.release === 'IN') {
      router.push(Config.siteBaseURL + `teamsports/${sportName}/odds/0/false`);
    } else {
      item?.sportId === 4 || item?.sportId === 12
        ? router.push(
          Config.siteBaseURL +
          `all-sports/${sportName}/${item?.sportId}/odds/${item?.id}/true?menu=fixtures_results&touranamentId=0&touranamentName=All+Leagues`,
        )
        : router.push(
          Config.siteBaseURL +
          `teamsports/${sportName}/odds/${item?.id}/true`,
        );
    }
  };
  // `/all-sports/${sportName}/${data?.SportId}/odds/0/false?menu=fixtures_results&touranamentId=0&touranamentName=All+Leagues`
  const handleGetDate = (dateString: any) => {
    const currentDate = moment();
    const tomorrowDate = moment().add(1, 'day');
    const date = moment(dateString);
    let result;

    if (currentDate.isSame(date, 'day')) {
      result = 'Today';
    } else if (tomorrowDate.isSame(date, 'day')) {
      result = 'Tomorrow';
    } else {
      result = date.format('ddd');
    }
    const formattedDate = date.format('DD/MM');
    const formattedTime = date.format('hh:mm A');
    return (
      <div className="flex justify-between items-center mt-2">
        <p className="text-white text-[11.42px] leading-[14px]">
          {result} {' ' + formattedDate}
        </p>
        <p className="text-white text-[11.42px] leading-[14px]">
          {formattedTime}
        </p>
      </div>
    );
  };

  const upcomingRacesSlides = sliderData;

  const handlePrevClick = () => {
    if (carouselRef.current) {

      carouselRef.current.slickPrev(); // use slickPrev to go to the previous slide
    }
    if (!isLive) {
      setTimeout(() => {
        if (!disablePrev) {
          const currentIndex = Math.ceil(activeSlide);
          if (
            currentIndex ===
            // @ts-expect-error

            48 - 2 * carouselRef.current?.props?.slidesToShow &&
            !prevData?.length
          ) {
            fetchpastResultdata(prevOffset + 1);
          } else if (currentIndex === 0) {
            setActiveSlide(currentIndex);
            setIsTopBarLoading(true);
            prevData ?? fetchpastResultdata(prevOffset + 1);
            prevData?.length && MergePrevData();
          }
        }
      }, 50);
    }
  };

  const handleNextClick = () => {
    if (carouselRef.current) {

      carouselRef.current.slickNext();
    }
    const currentIndex = Math.ceil(
      // @ts-expect-error

      activeSlide / carouselRef.current?.props?.slidesToShow,
    );
    const maxIndex =
      Math.ceil(
        // @ts-expect-error

        upcomingRacesSlides.length / carouselRef.current?.props?.slidesToShow,
      ) - 1;
    if (!disableNext) {
      if (currentIndex === maxIndex - 7 && !nextData?.length) {
        fetchNextResultdata(nextOffset + 1);
      } else if (currentIndex === maxIndex) {
        setActiveSlide(currentIndex);
        setIsTopBarLoading(true);
        nextData?.length && MergeNextData();
      }
    }
  };

  const determineSlidesToShow = (length: number | undefined): number => {
    if (!length || length < 1) return 6;
    if (length >= 6) return 6;
    if (length >= 5) return 5;
    if (length >= 4) return 4;
    if (length >= 3) return 3;
    if (length >= 2) return 2;
    return 1;
  };

  const slidesToShow = determineSlidesToShow(upcomingRacesSlides?.length);

  const settings = {
    dots: false,
    infinite: false,
    speed: 500,
    prevArrow: <PrevArrow onClick={() => handlePrevClick()} />,
    nextArrow: <NextArrow onClick={() => handleNextClick()} />,
    slidesToShow: slidesToShow,
    slidesToScroll: 6,
    initialSlide: sliderIndex,
    // arrows: false,
    responsive: [
      { breakpoint: 1120, settings: { slidesToShow: 3, slidesToScroll: 3 } },
      { breakpoint: 940, settings: { slidesToShow: 2, slidesToScroll: 2 } },
      { breakpoint: 560, settings: { slidesToShow: 1, slidesToScroll: 1 } },
    ],
  };

  const iconsMap: Record<string, any> = {
    'Horse Racing': Horse,
    'Harness Racing': Harnes,
    'Greyhound Racing': Greys,
    'American Football': Football,
    'Australian Rules': Ar,
    Baseball: Baseball,
    Basketball: Basketball,
    Boxing: Boxing,
    Cricket: Cricket,
    Golf: Golf,
    'Ice Hockey': IceHockey,
    'Mixed Martial Arts': Mma,
    'Rugby League': Rugby,
    'Rugby Union': Ru,
    Soccer: Soccer,
    Tennis: Tennis,
  };

  const raceIcon = (name: string): JSX.Element | null => {
    // Temporarily return text instead of SVG to fix React error
    return name ? (
      <span className="w-4 h-4 text-xs bg-gray-600 text-white rounded px-1 flex items-center justify-center">
        {name.charAt(0)}
      </span>
    ) : null;
  };

  const getTeamLogo = (flag: string | undefined): JSX.Element => {
    if (flag) {
      if (flag.includes('uploads')) {
        return (
          <Image
            src={Config.mediaURL + flag}
            className="rounded-[50%] h-full w-full"
            alt="team Icon"
            width={25}
            height={25}
            unoptimized={true}
          />
        );
      } else {
        return (
          <Image
            src={flag}
            className="rounded-[50%] h-full w-full"
            alt="team Icon"
            width={25}
            height={25}
            unoptimized={true}
          />
        );
      }
    } else {
      return (
        <Image
          className="rounded-[50%] h-full w-full"
          src={'/images/default-team-logo.png'}
          alt="Odds Icon"
          width={25}
          height={25}
          unoptimized={true}
        />
      );
    }
  };

  const fetchTeamlogo = (item: any, type: string): JSX.Element => {
    if (type === 'hometeam') {
      return getTeamLogo(item?.homeFlag);
    } else {
      return getTeamLogo(item?.awayFlag);
    }
  };

  const renderCricketScore = (
    runs: number | string | undefined,
    wickets: number | string | undefined,
    declared: number | undefined,
  ): string => {
    if (!runs && runs !== 0) return '-';
    let score = `${runs}`;
    if (wickets !== 10 && (wickets || wickets === 0)) {
      score += `/${wickets}`;
      if (declared === 1) score += 'd';
    }
    return score;
  };

  const fetchCricketScore = (teamScore: any, teamType: string): JSX.Element => {
    const isHomeTeam = teamType === 'hometeam';
    const isExtended = teamScore?.Exd > '1';

    const primaryRuns = isHomeTeam ? teamScore?.Tr1C1 : teamScore?.Tr2C1;
    const primaryWickets = isHomeTeam ? teamScore?.Tr1CW1 : teamScore?.Tr2CW1;
    const primaryDeclared = isHomeTeam ? teamScore?.Tr1CD1 : teamScore?.Tr2CD1;

    const secondaryRuns = isHomeTeam ? teamScore?.Tr1C2 : teamScore?.Tr2C2;
    const secondaryWickets = isHomeTeam ? teamScore?.Tr1CW2 : teamScore?.Tr2CW2;
    const secondaryDeclared = isHomeTeam
      ? teamScore?.Tr1CD2
      : teamScore?.Tr2CD2;

    return (
      <span className="py-0 px-[2.5px] break-all">
        {renderCricketScore(primaryRuns, primaryWickets, primaryDeclared)}
        {isExtended &&
          secondaryRuns !== undefined &&
          ` & ${renderCricketScore(secondaryRuns, secondaryWickets, secondaryDeclared)}`}
      </span>
    );
  };

  const fetchSoccerScore = (teamScore: any, teamtype: any) => {
    return teamtype === 'hometeam' ? (
      <span className="py-0 px-[2.5px] break-all">{teamScore?.Tr1 ?? '-'}</span>
    ) : (
      <span className="py-0 px-[2.5px] break-all">{teamScore?.Tr2 ?? '-'}</span>
    );
  };

  const renderTeamScore = (teamScore: any, sportId: number) => {
    if (!teamScore) {
      return <span className="py-0 px-[10px] text-sm text-white">-</span>;
    }

    if (sportId === 11) {
      return (
        <span className="py-0 px-[2.5px] break-all">
          {teamScore?.current || teamScore?.current === 0
            ? teamScore?.current
            : '-'}
        </span>
      );
    }

    if (sportId === 4) {
      return <span className="py-0 px-[10px] text-sm text-white">-</span>;
    }

    return (
      <span className="py-0 px-[2.5px] break-all">
        {teamScore?.current || teamScore?.current === 0
          ? teamScore?.current
          : '-'}
      </span>
    );
  };

  const fetchScoreHeader = (data: any, teamId: any, teamtype: any) => {
    const teamSportsScores =
      teamtype === 'hometeam' ? 'homeTeamScoreData' : 'awayTeamScoreData';
    const teamScore =
      typeof data?.[teamSportsScores] === 'string'
        ? JSON.parse(data?.[teamSportsScores])
        : data?.[teamSportsScores];

    return <>{renderTeamScore(teamScore, data?.sportId)}</>;
  };

  const renderScore = (slide: any) => {
    if (slide?.status === 'inprogress' || slide?.Epr === 1) {
      return (
        <div className="flex justify-between items-center mt-2">
          <p className="text-[11.42px] leading-[14px] font-semibold uppercase text-white bg-orange-500 py-[1px] px-1 rounded-[3px]">
            Live
          </p>
          <p className="text-[11.42px] leading-[14px] text-white">Scores</p>
        </div>
      );
    } else {
      return handleGetDate(slide?.eventDate);
    }
  };

  const renderRaceInfo = (slide: any) => {
    if (slide?.sportId === 1 || slide?.sportId === 2 || slide?.sportId === 3) {
      let resultContent = null;

      // Check if resultDisplay is available
      if (slide?.resultDisplay && slide?.resultDisplay !== '') {
        resultContent = (
          <span className="text-xs leading-[15px] bg-gray-100 text-black-800 font-semibold py-[3px] px-[7px] rounded-[3px]">
            {slide?.resultDisplay}
          </span>
        );
      }
      // If not, check if the event is closed
      else if (
        moment(moment.utc(slide?.eventDate).local().toDate()).isBefore(
          new Date(),
        )
      ) {
        resultContent = (
          <span className="text-xs leading-[15px] bg-orange-500 text-white font-semibold p-[3px] rounded-[3px]">
            Closed
          </span>
        );
      }
      // Otherwise, show countdown
      else {
        resultContent = (
          <Countdown
            date={moment.utc(slide?.eventDate).local().toDate()}
            renderer={renderer}
            onComplete={() => {
              setTimeout(() => {
                setPrevOffset(0);
                setNextOffset(1);
                setDisableNext(false);
                setDisablePrev(false);
                GetUpcomingRace();
              }, 2000);
            }}
          />
        );
      }

      return (
        <div className="flex justify-between items-center mt-[26px]">
          <span className="text-xs leading-[15px]">
            <span>{`R${slide?.raceNumber} ${slide?.trackName}`}</span>
          </span>
          {resultContent}
        </div>
      );
    }

    return null;
  };

  const renderTeamInfo = (slide: any, teamType: string) => {
    const team = teamType === 'hometeam' ? slide?.homeName : slide?.awayName;

    // Determine the team logo
    let teamLogo;
    if (teamType === 'hometeam') {
      teamLogo = fetchTeamlogo(slide, 'hometeam');
    } else {
      teamLogo = fetchTeamlogo(slide, 'awayteam');
    }

    // Determine the team score
    let teamScore;
    if (slide?.sportId === 4) {
      if (slide?.sportId === 4) {
        teamScore = fetchCricketScore(slide, teamType);
      } else if (slide?.sportId === 8) {
        teamScore = fetchSoccerScore(slide, teamType);
      } else {
        teamScore = fetchScoreHeader(slide, team?.teamId, teamType);
      }
    } else {
      teamScore = fetchScoreHeader(slide, team?.teamId, teamType);
    }

    return (
      <div className="flex items-center justify-start mt-2">
        <div className="w-[25px] h-[25px] min-w-[25px] object-contain bg-white rounded-[50%]">
          {teamLogo}
        </div>
        <div className="flex items-center">
          <p className="text-[11.42px] text-left leading-[14px] text-white ml-[5.4px] line-clamp-2 uppercase w-full">
            {team || ''}
          </p>
        </div>
        <div className="flex items-center gap-1">
          <p className="text-[11.42px] leading-[14px] text-white font-medium w-max">
            {teamScore}
          </p>
          <span className="w-[7px] h-[11px] flex">
            {slide?.winnerCode && slide?.winnerCode === 1 && <WinArrowIcon />}
          </span>
        </div>
      </div>
    );
  };

  const renderSliderItem = (slide: any, i: number) => {
    return (
      <button
        key={i}
        className="p-[9px] border-r-[1px] border-r-black-300 h-full cursor-pointer"
        onClick={() => handleRaceNavigation(slide)}
      >
        <span>
          <p className="flex justify-start items-center">
            <span>
              {raceIcon(slide?.sportName ? slide?.sportName.trim() : '')}
            </span>
            <span className="text-xs text-white pl-1 font-semibold line-clamp-1">
              {slide?.sportId === 1 ||
                slide?.sportId === 2 ||
                slide?.sportId === 3
                ? slide?.sportName || ''
                : slide?.tournamentName || ''}
            </span>
          </p>

          {renderScore(slide)}

          {/* For Racing */}
          {renderRaceInfo(slide)}

          {/* For Team-related sports */}
          {renderTeamInfo(slide, 'hometeam')}
          {renderTeamInfo(slide, 'awayteam')}
        </span>
      </button>
    );
  };

  // Debug logging
  console.log('UpcomingCarousel Debug:', {
    isTopBarLoading,
    slidesLength: upcomingRacesSlides?.length,
    sliderData: sliderData?.length,
    activeSlide,
    sliderIndex
  });

  return (
    <>
      {isTopBarLoading ? (
        <div className="flex justify-center items-center w-full h-full min-h-[130px]">
          <div className="text-sm text-white p-1">
            <Loader />
          </div>
        </div>
      ) : (
        <>
          {upcomingRacesSlides?.length > 0 ? (
            <div className="w-auto overflow-hidden px-7 h-full min-h-[130px]">
              {/* @ts-ignore */}
              <Slider
                className="next-to-jump-slider h-full bg-jump-gradient"
                ref={carouselRef}
                {...settings}
                beforeChange={(_, newIndex) => setActiveSlide(newIndex)}
              >
                {upcomingRacesSlides.map((slide, i) =>
                  renderSliderItem(slide, i),
                )}
              </Slider>


            </div>
          ) : (
            <div className="flex justify-center items-center w-full h-full min-h-[130px]">
              <p className="text-sm text-white p-1">
                {upcomingRacesSlides?.length === 0
                  ? "There are currently no scheduled matches and races available from your selection."
                  : "Loading upcoming events..."
                }
              </p>
            </div>
          )}
        </>
      )}
    </>
  );
};

export default UpcommingCarousel;
