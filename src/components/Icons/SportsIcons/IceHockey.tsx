import React from 'react';

const IceHockey = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20.394"
      height="18"
      viewBox="0 0 20.394 18"
    >
      <g id="Group_92311" data-name="Group 92311" transform="translate(0 0)">
        <g id="Group_92312" data-name="Group 92312" transform="translate(0 0)">
          <path
            id="Path_122917"
            data-name="Path 122917"
            d="M34.767,12.224l-.779-.34c.648-1.484,1.8-3.968,2.643-5.783.368-.793.664-1.433.81-1.754.127-.281.391-.964.7-1.754S38.814.847,39.16,0l.787.322C39.6,1.163,39.24,2.1,38.93,2.9c-.323.835-.579,1.5-.716,1.8-.146.323-.444.965-.813,1.761-.84,1.811-1.99,4.291-2.635,5.766"
            transform="translate(-19.552 0)"
            fill="#003764"
          />
          <path
            id="Path_122918"
            data-name="Path 122918"
            d="M6.756,15.575H3.879C1.666,15.575,0,12.615,0,10.7A2.157,2.157,0,0,1,.377,9.374a1.522,1.522,0,0,1,1.282-.594h8.919c1.483,0,1.75-.211,2.169-1.041.088-.174.268-.516.508-.973C13.97,5.405,15.3,2.873,16.2.954l.769.36c-.908,1.937-2.246,4.481-2.964,5.847-.237.45-.415.788-.5.96-.645,1.28-1.328,1.507-2.928,1.507H1.659a.7.7,0,0,0-.6.255,1.36,1.36,0,0,0-.206.812c0,1.555,1.386,4.03,3.029,4.03H6.756Z"
            transform="translate(0 -0.549)"
            fill="#003764"
          />
          <path
            id="Path_122919"
            data-name="Path 122919"
            d="M20.427,33.174c-2.677,0-5.522-.744-5.522-2.124V28.5c0-1.38,2.845-2.124,5.522-2.124s5.522.744,5.522,2.124V31.05c0,1.38-2.845,2.124-5.522,2.124m0-5.946c-3.083,0-4.672.893-4.672,1.274V31.05c0,.381,1.589,1.274,4.672,1.274S25.1,31.431,25.1,31.05V28.5c0-.381-1.589-1.274-4.672-1.274"
            transform="translate(-8.574 -15.174)"
            fill="#003764"
          />
          <path
            id="Path_122920"
            data-name="Path 122920"
            d="M22.523,32.468a11.731,11.731,0,0,1-3.973-.617l.3-.793a10.866,10.866,0,0,0,3.67.561,10.871,10.871,0,0,0,3.671-.561l.3.793a11.735,11.735,0,0,1-3.975.617"
            transform="translate(-10.671 -17.866)"
            fill="#003764"
          />
        </g>
      </g>
    </svg>
  );
};

export default IceHockey;
