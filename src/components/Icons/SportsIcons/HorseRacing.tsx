import React from 'react';

const HorseRacing = () => {
  return (
    <svg
      id="Group_136879"
      data-name="Group 136879"
      xmlns="http://www.w3.org/2000/svg"
      width="22"
      height="22"
      viewBox="0 0 22 22"
    >
      <rect
        id="Rectangle_6938"
        data-name="Rectangle 6938"
        width="22"
        height="22"
        fill="none"
      />
      <g
        id="Group_24112"
        data-name="Group 24112"
        transform="translate(0 4.074)"
      >
        <path
          id="Union_86"
          data-name="Union 86"
          d="M12.666,13.481a.2.2,0,0,1-.1-.2.158.158,0,0,0-.042-.169c-.039-.027-.076-.056-.114-.085,0,.154-.039.277-.1.277a.051.051,0,0,1-.023,0c-.1-.039-.277-.135-.4-.191s-.344-.175-.344-.259a.813.813,0,0,0,0-.088A.739.739,0,0,0,11.5,12.5c-.053-.095-.878-.671-1.153-.881a11.189,11.189,0,0,0-1.824-.793.5.5,0,0,1-.331-.542.873.873,0,0,1,0-.092,9.655,9.655,0,0,0,.069-1.036V9.013c-.014-.659-1.221-1.163-2.018-1.7a1.676,1.676,0,0,1-.568-.589,2.4,2.4,0,0,1-.352.28c-.1.058-.4.072-.473.13s-.206.278-.32.4-.283.428-.428.6a.455.455,0,0,1-.3.187c-.111.05.034-.068.121-.153s.138-.179.058-.121a.72.72,0,0,1-.275.09c-.08.016.027-.056.122-.114A1.425,1.425,0,0,0,4.1,7.837a3.852,3.852,0,0,0,.378-.523c.087-.13.109-.3.072-.3a.6.6,0,0,0-.225.114A2.22,2.22,0,0,0,4,7.548c-.095.138-.2.375-.248.455a3.348,3.348,0,0,1-.3.281A.872.872,0,0,1,3.1,8.46c-.119.05-.256.027-.095-.016a1.146,1.146,0,0,0,.331-.159,1.807,1.807,0,0,0,.232-.225,3.166,3.166,0,0,1-.3.187,2.031,2.031,0,0,1-.516.172c-.106.023-.331-.008-.209-.014a1.544,1.544,0,0,0,.3-.043,1.705,1.705,0,0,0,.484-.217A1.868,1.868,0,0,0,3.8,7.67c.071-.164-.195.056-.253.137s-.183.188-.233.253-.248.119-.191.061.2-.122.253-.209,0-.08-.092.011a1.6,1.6,0,0,1-.473.24,3.336,3.336,0,0,1-.756.225c-.22.019-.712.092-.851.122a.679.679,0,0,0-.29.137C.843,8.7.756,8.7.867,8.616a.934.934,0,0,1,.267-.137,7.561,7.561,0,0,1,.823-.195c.111-.023.3-.106.253-.106A.856.856,0,0,1,1.856,8.1c-.2-.072.109-.023.29-.014a.8.8,0,0,0,.4-.058,1.575,1.575,0,0,0,.4-.256c.116-.1.072-.156-.042-.076a2.149,2.149,0,0,1-.695.317A4.151,4.151,0,0,1,1.44,8c-.095,0-.214-.092-.32-.092a1.935,1.935,0,0,0-.394.137c-.108.034-.367.2-.511.267A3.757,3.757,0,0,1,.9,7.9c.29-.138.1-.111.023-.08S.658,7.9.562,7.953a2.189,2.189,0,0,0-.392.29c-.108.095-.2.175-.158.095a2.649,2.649,0,0,1,.347-.325,2.835,2.835,0,0,1,.63-.273C1.12,7.7.943,7.717.943,7.717a1.442,1.442,0,0,1-.584-.211C.233,7.414.241,7.4.328,7.45A1.874,1.874,0,0,0,.6,7.617a1.253,1.253,0,0,0,.573.026c.169-.064-.042-.079-.167-.079a6.239,6.239,0,0,1-.73-.238C.1,7.278.233,7.255.359,7.3s.367.146.484.177a1.393,1.393,0,0,0,.375.026,1.123,1.123,0,0,1-.331-.122A1.93,1.93,0,0,0,.436,7.2C.322,7.178.184,7.125.241,7.131s.145.013.217.024a6.361,6.361,0,0,1,.6.236c.122.039.565.064.447.039a1.344,1.344,0,0,1-.267-.047C1.1,7.334.687,7.131.57,7.109s-.283-.05-.188-.05a1.341,1.341,0,0,1,.363.066c.122.039.484.145.2.014S.684,7.033.779,7.053a2.289,2.289,0,0,1,.4.117,1.884,1.884,0,0,0,.622.058c.217-.008.244-.064.08-.064s-.153-.039-.312-.047a3.029,3.029,0,0,1-.745-.259.841.841,0,0,0-.267-.13c-.111-.034-.206-.1-.1-.08a.365.365,0,0,0,.281-.034A.933.933,0,0,1,1,6.491c.122-.031.072-.058-.08-.111s.206-.008.267.023-.018-.08-.19-.087a2.381,2.381,0,0,0-.428.006C.455,6.339.314,6.316.386,6.3s.462-.031.556-.031a4.481,4.481,0,0,1,.547.08A2.185,2.185,0,0,1,2,6.556a1.3,1.3,0,0,0,.339.164,1.2,1.2,0,0,1-.175-.148c-.084-.08-.1-.145-.011-.055a1.625,1.625,0,0,0,.492.3c.084,0,.29-.071.236-.071s-.159-.043-.29-.066a.881.881,0,0,1-.375-.236c-.071-.08-.087-.225-.061-.159a.861.861,0,0,0,.333.32,1.656,1.656,0,0,0,.537.08,4.643,4.643,0,0,0,1.016-.6,5.18,5.18,0,0,0,1.037-.918,2.047,2.047,0,0,1,.83-.573,3.35,3.35,0,0,1,1.478-.97.408.408,0,0,1,.069-.019,3.487,3.487,0,0,1,.814-.1h.045a3.542,3.542,0,0,1,.976.137c.315.05,1.248.151,1.969.235a1.023,1.023,0,0,1-.2-.177c-.058-.066-.013-.114.068-.093.138,0,.272.016.4.031a2.849,2.849,0,0,0,.347.037l0-.008a1.823,1.823,0,0,1-.254-.146c-.236-.15-.515-.344-.5-.695A1.088,1.088,0,0,1,11.4,2.08c.191-.206.236-.256.333-.362a.473.473,0,0,1,.133-.111c.032-.026.066-.051.1-.076a4.715,4.715,0,0,1,1.287-.5A7.38,7.38,0,0,1,14.367.942c.193.008.61.008.777.024A.524.524,0,0,1,15.17.912c.064-.138.018-.179.084-.37a.747.747,0,0,1,.312-.405A.969.969,0,0,1,16.047,0a.994.994,0,0,1,.679.325.664.664,0,0,1,.1.148c.051,0,.084,0,.1.031a.5.5,0,0,1,.061.306c-.016.077-.035.093-.072.092a1.868,1.868,0,0,1,0,.19.5.5,0,0,0-.093-.01.278.278,0,0,1-.064.167c-.04.027-.037.042,0,.137s.058.142.074.183,0,.08-.069.069-.106-.008-.111.053,0,.084-.031.092-.05.023-.027.053-.011.039-.031.069,0,.092,0,.114a.091.091,0,0,1-.095.069c-.061-.008-.206-.031-.253-.031-.1,0-.2-.008-.3,0,0,0,0,0-.006,0-.2.021-.479.037-.46.08a1.028,1.028,0,0,1,.021.42c-.027.153-.13.188.026.317s.153.2.225.174a.335.335,0,0,0-.153-.135c-.129-.05.008-.1.127-.127s-.079-.151-.166-.2a.175.175,0,0,0,.1,0c.085-.035.1-.124-.023-.172s.077-.043.172-.019.145-.035.042-.085.457.047.565.047c.079,0,.146,0,.227,0,.08-.053.08-.058.148,0,.133-.016.318-.087.187-.093s.122-.145.248-.172a.27.27,0,0,0,.191-.232c0-.061.137-.043.27-.043s.137-.082.095-.137.166-.034.283,0a.461.461,0,0,1,.238.124l.026-.013c.106-.045.175-.088.175-.122,0-.014-.011-.031-.042-.042-.092-.047-.161-.175-.291-.3a.2.2,0,0,1-.031-.034.123.123,0,0,1-.018-.026c-.019-.039,0-.055.042-.058h.037a.454.454,0,0,1,.1.019,1.385,1.385,0,0,0,.187.039h.008s0-.008,0-.011a0,0,0,0,1,0,0s0,0,0,0a.147.147,0,0,1-.027-.06c-.006-.027-.006-.058.008-.066h.014v0h.008s0,0,0,0a.012.012,0,0,0,.008,0c.079.055.373.142.465.2a.153.153,0,0,0,.08.04.121.121,0,0,0,.042.008.164.164,0,0,0,.08-.023.185.185,0,0,1,.125-.045h.011a.08.08,0,0,1,.027,0c.106.006.133.133.2.209a.128.128,0,0,0,.074.039h.018c.024,0,.047,0,.069,0a.325.325,0,0,1,.069-.008h.026c.1.011.169.145.378.312a4.078,4.078,0,0,1,.616.8,1.974,1.974,0,0,0,.206.33c.117.148.674.91.746,1s.166.161.256.256A.15.15,0,0,1,22,4.351a.286.286,0,0,1-.114.217.15.15,0,0,0-.053.133c0,.039,0,.076,0,.111,0,.019,0,.034,0,.05a.1.1,0,0,1-.031.053.009.009,0,0,1-.008.008.362.362,0,0,1-.151.09.22.22,0,0,1-.085.016.1.1,0,0,1-.034,0,.137.137,0,0,0-.047-.013h0c-.042,0-.064.039-.08.088-.006.019-.05.031-.1.031h-.026c-.093,0-.214-.014-.272-.014-.1,0-.095-.122-.129-.211a.178.178,0,0,0-.061-.066c-.1-.013-.507-.077-.622-.114s-1.016-.137-1.29-.179c-.125-.021-.333-.058-.54-.095a1.364,1.364,0,0,0-.193.336,6.72,6.72,0,0,1-.61.946.879.879,0,0,0-.154.325s-.021.232-.051.579a4.491,4.491,0,0,1-.283,1.179,1.372,1.372,0,0,1-.3.5,2.518,2.518,0,0,0,.727.418c.446.19.737.335.851.376a.533.533,0,0,1,.333.317,1.466,1.466,0,0,1,.17.664,11.213,11.213,0,0,0-.114,1.332,1.674,1.674,0,0,1-.087.684,4.916,4.916,0,0,0-.275.778c0,.1-.027.148-.187.362s-.175.347-.363.1a.908.908,0,0,1-.2-.706c.043-.191.2-.172.314-.219a.3.3,0,0,0,.148-.388.81.81,0,0,1,.043-.474c.031-.117.1-1.06.1-1.29a.6.6,0,0,0-.129-.449.912.912,0,0,0-.519-.214c-.146,0-.648-.047-.878-.047s-.724-.088-.928-.088a1.457,1.457,0,0,1-.674-.187h0a.277.277,0,0,1-.034.011s.3.536.409.684a5.291,5.291,0,0,0,.606.909.294.294,0,0,1,.13.253,1.175,1.175,0,0,1-.035.217c-.018.106-.056.153-.206.384a.231.231,0,0,1-.056.072.34.34,0,0,1-.2.1c-.014,0-.031,0-.045,0s-.031,0-.05,0l-.047-.011a2.167,2.167,0,0,0-.465.109c-.217.069-.878.317-1.126.359-.011,0-.023,0-.035,0h-.018a.289.289,0,0,1-.117-.024.839.839,0,0,1-.3-.225c-.034-.037-.064-.072-.095-.1a1.02,1.02,0,0,0-.437-.232,3.028,3.028,0,0,1-.481-.37c-.108-.08-.146-.138-.146-.18a.07.07,0,0,1,.05-.069.7.7,0,0,1,.436-.148h.018a.1.1,0,0,1,.039,0H12.9a.613.613,0,0,1,.489.278.093.093,0,0,1,.014.055c0,.042-.014.084-.014.122a.107.107,0,0,0,.069.1c.006,0,.01,0,.019.008a.182.182,0,0,0,.04.019c.039.016.077.031.111.042a.389.389,0,0,0,.058.016,1.127,1.127,0,0,0,.293.04H14c.034,0,.072,0,.106,0A4.438,4.438,0,0,0,15,11.57c.259-.066.4-.164.4-.206,0-.023.014-.069.014-.122a.177.177,0,0,0-.058-.142,4.757,4.757,0,0,0-.809-.565,10.158,10.158,0,0,1-1.026-.9c-.111-.074-.359-.169-.37-.264s-.008-.233-.084-.259-.37-.069-.741-.138a13.707,13.707,0,0,1-1.357-.359,6.242,6.242,0,0,1-.783-.409c.013.41.093,1.468.093,1.8a2.2,2.2,0,0,0,.471,1.185l.04.035c.145.069.29.133.437.195a.956.956,0,0,1,.428.312,3.286,3.286,0,0,0,.45.471.887.887,0,0,1,.143.158,4.49,4.49,0,0,0,.5.341,1.748,1.748,0,0,1,.8.526c.24.33.142.275-.233.275a2.326,2.326,0,0,1-.236.011A2.892,2.892,0,0,1,12.666,13.481Zm6.683-9.02c.113.019.473.076.836.132a7.056,7.056,0,0,0-.695-.346c-.146-.027-.283-.13-.447-.15h-.055a.813.813,0,0,0-.4.119.067.067,0,0,0-.019.014.94.94,0,0,0-.095.082C18.866,4.38,19.233,4.441,19.348,4.462ZM14.787,3.209a.65.65,0,0,1-.122.619.527.527,0,0,1-.132.093c.154-.021.307-.047.462-.074.051-.013.106-.019.159-.031.018-.014.037-.027.055-.04a1.282,1.282,0,0,1-.119-.159c-.055-.087-.172-.335-.2-.4a2,2,0,0,1-.151-.283c-.035-.1-.085-.111-.191-.095a.917.917,0,0,1-.317.026l-.047-.006C14.4,2.95,14.739,3.086,14.787,3.209Zm.725-.629c-.031-.008-.035-.019.011,0h0A.038.038,0,0,1,15.513,2.58Z"
          transform="translate(0 0)"
          fill="#003764"
        />
      </g>
    </svg>
  );
};

export default HorseRacing;
