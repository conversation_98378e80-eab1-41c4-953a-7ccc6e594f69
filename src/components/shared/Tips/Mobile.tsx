import React from 'react'
import { Trophy } from 'lucide-react'
export const TipOfTheDayMobile = () => {
  return (
    <div className="max-w-sm mx-auto bg-[#0a0b15] text-white rounded-xl overflow-hidden shadow-lg">
      {/* Header section with Best Bet badge and title */}
      <div className="relative px-4 pt-4 pb-2">
        <div className="absolute top-0 left-0 bg-orange-500 text-white px-3 py-1 rounded-br-lg font-bold text-sm">
          BEST BET
        </div>
        <div className="mt-6 flex justify-between items-start">
          <h2 className="text-2xl font-extrabold">TIP OF THE DAY</h2>
          <div className="bg-gradient-to-br from-purple-500 to-blue-600 p-2 rounded-full">
            <Trophy className="h-6 w-6 text-white" />
          </div>
        </div>
        {/* Horse details */}
        <div className="mt-2">
          <p className="text-blue-300 font-semibold">
            Legionnaire (Pakenham Race 4 No. 1)
          </p>
          <p className="text-gray-300 mt-2 text-sm">
            Racing well and gets all the favours from barrier 1. Doesn't win out
            of turn but strikes a suitable race and <PERSON> is riding really
            well.
          </p>
        </div>
      </div>
      {/* Race details section */}
      <div className="bg-[#1a1b36] mt-3 p-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <svg
              className="h-5 w-5 mr-2 text-white"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
            >
              <path
                d="M3 19l4-6 4 2 4-7 4 3"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            <span className="font-bold text-lg">PAKENHAM [AUS]</span>
          </div>
          <div className="bg-blue-600 text-white px-2 py-1 rounded font-bold text-sm">
            R4
          </div>
        </div>
        <div className="flex justify-between mt-2 text-sm">
          <div>Jump time: 10:55am</div>
          <div>Distance: 1710m</div>
        </div>
        {/* Runner details */}
        <div className="mt-4 border-t border-gray-700 pt-3">
          <div className="flex items-start">
            <div className="mr-4">
              <p>1. [Runner Name] (1)</p>
              <p className="text-xs text-gray-400">W: [00] kg</p>
              <p className="text-xs text-gray-400">T: [Trainer]</p>
            </div>
            <div className="text-xs text-gray-400">
              <p>J: [Jockey]</p>
            </div>
          </div>
        </div>
      </div>
      {/* Compare button */}
      <button className="w-full bg-[#3d4299] hover:bg-[#2d327a] text-white py-3 font-bold">
        COMPARE WITH SMARTODDS
      </button>
    </div>
  )
}
