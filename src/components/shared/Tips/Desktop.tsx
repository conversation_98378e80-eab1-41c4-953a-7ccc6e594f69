import React from 'react'
import { Trophy } from 'lucide-react'
export function TipOfTheDay() {
  return (
    
    <div className="w-full max-w-7xl mx-auto bg-[#0a0c24] text-white rounded-lg overflow-hidden shadow-xl">
      <div className="flex p-6">
        {/* Left section with trophy and tip info */}
        <div className="flex-1">
          <div className="flex items-center gap-4 mb-4">
            <div className="relative">
              <div className="absolute inset-0 bg-purple-500 opacity-50 rounded-full blur-sm"></div>
              <Trophy
                className="w-12 h-12 relative z-10 text-white"
                style={{
                  fill: 'url(#trophy-gradient)',
                }}
              />
              <svg width="0" height="0">
                <linearGradient
                  id="trophy-gradient"
                  x1="0%"
                  y1="0%"
                  x2="100%"
                  y2="100%"
                >
                  <stop stopColor="#8b5cf6" offset="0%" />
                  <stop stopColor="#3b82f6" offset="100%" />
                </linearGradient>
              </svg>
            </div>
            <h1 className="text-4xl font-bold tracking-wider">
              TIP OF THE DAY
            </h1>
          </div>
          <h2 className="text-xl font-bold mb-1">
            Legionnnaire (Pakenham Race 4 No. 1)
          </h2>
          <p className="text-gray-300">
            Racing well and gets all the favours from barrier 1. Doesn't win Out
            of turn but strikes a suitable race and Kennedy is riding really
            well.
          </p>
        </div>
        {/* Right section with race details */}
        <div className="ml-6 w-[400px]">
          <div className="bg-[#0a2d4d] rounded-md p-3 mb-3 flex items-center justify-between">
            <div className="flex items-center">
              <svg
                className="w-6 h-6 mr-2"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M19 5L5 19M5 5L19 19"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <circle
                  cx="12"
                  cy="12"
                  r="9"
                  stroke="currentColor"
                  strokeWidth="2"
                />
              </svg>
              <span className="font-bold text-lg">PAKENHAM (AUS)</span>
            </div>
            <span className="bg-blue-600 px-2 py-1 rounded text-sm font-medium">
              R4
            </span>
          </div>
          <div className="bg-white text-black rounded-md p-4">
            <div className="flex justify-between text-sm mb-4">
              <span>Jump time: 10:55am</span>
              <span>|</span>
              <span>Distance: 1710m</span>
            </div>
            <div className="mb-4">
              <div className="font-bold">1. [Runner Name] (1)</div>
              <div className="flex text-sm text-gray-600">
                <div className="w-1/2">W: [00] kg</div>
                <div className="w-1/2">J: [Jockey]</div>
              </div>
              <div className="text-sm text-gray-600">T: [Trainer]</div>
            </div>
            <button className="w-full bg-[#1e3a8a] text-white font-bold py-3 rounded text-center">
              COMPARE WITH SMARTODDS
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
