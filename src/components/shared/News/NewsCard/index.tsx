import React from 'react'
import { BookmarkIcon } from 'lucide-react'
interface NewsCardProps {
  title: string
  image: string
  tag: string
  time: string
  variant: 'large' | 'small'
}
export function NewsCard({ title, image, tag, time, variant }: NewsCardProps) {
  return (
    <div className="relative flex flex-col h-full">
      {/* Image container */}
      <div
        className={`relative ${variant === 'large' ? 'aspect-[16/9] md:aspect-[16/10]' : 'aspect-[16/9]'} mb-4 overflow-hidden rounded-md`}
      >
        <img src={image} alt={title} className="w-full h-full object-cover" />
        <div className="absolute bottom-4 left-4">
          <span className="bg-orange-500 text-white px-3 py-1 text-sm font-medium uppercase rounded">
            {tag}
          </span>
        </div>
      </div>
      {/* Content */}
      <div className="flex-grow">
        <h3
          className={`font-bold ${variant === 'large' ? 'text-2xl mb-2' : 'text-lg mb-1'}`}
        >
          {title}
        </h3>
      </div>
      {/* Footer */}
      <div className="flex justify-between items-center mt-2">
        <span className="text-gray-500 text-sm">{time}</span>
        <button className="text-gray-400 hover:text-gray-700">
          <BookmarkIcon size={18} />
        </button>
      </div>
    </div>
  )
}
