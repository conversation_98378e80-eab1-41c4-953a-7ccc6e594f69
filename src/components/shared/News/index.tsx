'use client'
import React, { useState } from 'react'
import { NewsCard } from './NewsCard'
import { BookmarkIcon } from 'lucide-react'
type Category = 'ALL' | 'RACING' | 'SPORT'
export default function NewsSection() {
  const [activeCategory, setActiveCategory] = useState<Category>('ALL')
  const newsItems = [
    {
      id: 1,
      title:
        'Featured news title featured news title featured news title featured news title featured',
      image:
        'https://images.unsplash.com/photo-1546422904-90eab23c3d7e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1600&q=80',
      tag: 'NEWS',
      time: '27min',
      featured: true,
    },
    {
      id: 2,
      title:
        'Featured news title featured news title featured news title featured',
      image:
        'https://images.unsplash.com/photo-1551958219-acbc608c6377?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80',
      tag: 'RACING',
      time: '6hr',
      featured: false,
    },
    {
      id: 3,
      title:
        'Featured news title featured news title featured news title featured',
      image:
        'https://images.unsplash.com/photo-1517649763962-0c623066013b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80',
      tag: 'SPORT',
      time: '6hr',
      featured: false,
    },
    {
      id: 4,
      title:
        'Featured news title featured news title featured news title featured',
      image:
        'https://images.unsplash.com/photo-1508098682722-e99c43a406b2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80',
      tag: 'NEWS',
      time: '6hr',
      featured: false,
    },
    {
      id: 5,
      title:
        'Featured news title featured news title featured news title featured',
      image:
        'https://images.unsplash.com/photo-1552667466-07770ae110d0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80',
      tag: 'RACING',
      time: '6hr',
      featured: false,
    },
  ]
  const categories: Category[] = ['ALL', 'RACING', 'SPORT']
  return (
    <section className="container mx-auto px-4 py-8 max-w-7xl">
        <h2 className="text-3xl md:text-5xl mb-6 font-apotek-comp">FEATURED NEWS</h2>
      {/* Category Navigation */}
      <div className="flex border-b border-gray-200 mb-6">
        {categories.map((category) => (
          <button
            key={category}
            className={`pb-2 px-4 font-apotek-comp  text-3xl ${activeCategory === category ? 'text-black border-b-2 border-red-500' : 'text-gray-400'}`}
            onClick={() => setActiveCategory(category)}
          >
            {category}
          </button>
        ))}
      </div>
      {/* News Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Featured large news card */}
        <div className="lg:col-span-2">
          {newsItems
            .filter((item) => item.featured)
            .map((item) => (
              <NewsCard
                key={item.id}
                title={item.title}
                image={item.image}
                tag={item.tag}
                time={item.time}
                variant="large"
              />
            ))}
        </div>
        {/* Smaller news cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6">
          {newsItems
            .filter((item) => !item.featured)
            .map((item) => (
              <NewsCard
                key={item.id}
                title={item.title}
                image={item.image}
                tag={item.tag}
                time={item.time}
                variant="small"
              />
            ))}
        </div>
      </div>
    </section>
  )
}


