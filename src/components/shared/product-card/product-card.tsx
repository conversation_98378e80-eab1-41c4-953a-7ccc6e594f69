"use client"

import React, { useEffect, useState } from 'react'
import smartOdds from '../../../../public/images/odds-card.png'
import { StaticImageData } from 'next/image'
import smartPlays from '../../../../public/images/smart-play-card.png'
import smartTipCard from '../../../../public/images/smart-tip-card.png'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import {
  Carousel,
  CarouselApi,
  CarouselContent,
  CarouselItem,
} from '@/components/ui/carousel'

type ProductCardProps = {
  backgroundImage: StaticImageData
  accentText: string
  mainText: string
  buttonText: string
  buttonColor: string
  borderColor: string
}

const ProductCard = ({
  backgroundImage,
  accentText,
  mainText,
  buttonText,
  buttonColor,
  borderColor,
}: ProductCardProps) => {
  return (
    <Card className="relative overflow-hidden rounded-lg h-[267px] shadow-none border-0">
      <CardContent 
        className="p-0 h-full shadow-md"
        style={{
          backgroundImage: `url(${backgroundImage.src})`,
          backgroundSize: 'contain',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center',
        }}
      >
        <div className="absolute inset-0 flex flex-col justify-center items-center text-center">
          <div className="mb-2">
            <div className="text-[#FF5733] tracking-tight text-headline-mobile-2 md:text-headline-2 font-bold leading-none font-termina">
              {accentText}
            </div>
            <div className="text-white text-headline-mobile-1 md:text-headline-1 leading-none font-apotek-comp">
              {mainText}
            </div>
          </div>
          <Button
            size={"lg"}
            className={`p-6 px-2 text-white font-apotek-comp rounded-md text-3xl uppercase ${buttonColor} ${borderColor}`}
          >
            {buttonText}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

const ProductCards = () => {
  const [api, setApi] = useState<CarouselApi>()
  const [activeIndex, setActiveIndex] = useState(0)

  useEffect(() => {
    if (!api) {
      return
    }
    setActiveIndex(api.selectedScrollSnap())
    api.on("select", () => {
      setActiveIndex(api.selectedScrollSnap())
    })
  }, [api])

  const cards = [
    {
      backgroundImage: smartOdds,
      accentText: "ODDS",
      mainText: "COMPARISON",
      buttonText: "GET BEST ODDS",
      buttonColor: "bg-primary-gradient",
      borderColor: "border-primaryborder border-[1px]"
    },
    {
      backgroundImage: smartPlays,
      accentText: "FREE",
      mainText: "DAILY FANTASY",
      buttonText: "PLAY FANTASY FREE",
      buttonColor: "bg-secondary-gradient",
      borderColor: "border-secondaryborder border-[1px]"
    },
    {
      backgroundImage: smartTipCard,
      accentText: "FREE",
      mainText: "TIPPING COMPS",
      buttonText: "JOIN TIPPING FREE",
      buttonColor: "bg-dark-primary",
      borderColor: "border-darkprimaryborder border-[1px]"
    }
  ]

  return (
    <div className="w-full max-w-full mx-auto p-4">
      {/* Desktop Grid View */}
      <div className="hidden lg:grid grid-cols-3 gap-4">
        {cards.map((card, index) => (
          <ProductCard key={index} {...card} />
        ))}
      </div>

      {/* Tablet and Mobile Carousel View */}
      <div className="lg:hidden">
        <Carousel 
          className="w-full" 
          opts={{ 
            loop: true,
            align: "start"
          }} 
          setApi={setApi}
        >
          <CarouselContent>
            {cards.map((card, index) => (
              <CarouselItem key={index} className="md:basis-1/2 basis-full">
                <div className="p-1">
                  <ProductCard {...card} />
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
        </Carousel>
        {/* indicator */}
        <div className="flex justify-center items-center gap-2">
          {cards.map((_, index) => (
            // active indicator
            <div key={index} className="w-[18px] h-[3px] rounded-full bg-secondary" style={{
              backgroundColor: index === activeIndex ? '#FC4714' : '#B2C2D0',
            }} />
          ))}
        </div>
      </div>
    </div>
  )
}

export default ProductCards