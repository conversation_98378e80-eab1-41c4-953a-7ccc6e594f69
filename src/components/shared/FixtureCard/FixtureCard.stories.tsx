import { Meta, StoryObj } from '@storybook/react'
import FixtureCard from './FixtureCard'

const meta: Meta<typeof FixtureCard> = {
  title: 'Shared/FixtureCard',
  component: FixtureCard,
}

export default meta

type Story = StoryObj<typeof FixtureCard>

export const Default: Story = {
  args: {
    homeTeam: { name: 'Team A', logo: 'https://media.smartb.com.au/uploads/1742339245509.png' },
    awayTeam: { name: 'Team B', logo: 'http://media.smartb.com.au/uploads/1742337624947.png' },
    time: '10:00',
    date: '2021-01-01',
  },
}