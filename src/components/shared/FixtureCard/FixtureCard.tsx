import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"

export default function FixtureCard() {
  return (
    <Card className="w-full max-w-md mx-auto bg-white">
      {/* Header Section */}
      <div className="flex items-center justify-between p-6 pb-4">
        {/* Roosters Logo */}
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mb-2">
            <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center">
              <span className="text-red-600 font-bold text-xs">🐓</span>
            </div>
          </div>
        </div>

        {/* Match Details */}
        <div className="flex flex-col items-center text-center">
          <div className="text-gray-600 text-sm font-medium mb-1">NRL</div>
          <div className="text-3xl font-bold text-gray-900 mb-1">6:30PM</div>
          <div className="text-gray-600 text-sm">6 MAR</div>
        </div>

        {/* Broncos Logo */}
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-700 rounded-full flex items-center justify-center mb-2">
            <div className="w-12 h-12 flex items-center justify-center">
              <span className="text-white font-bold text-lg">🐴</span>
            </div>
          </div>
        </div>
      </div>

      {/* Team Names */}
      <div className="flex items-center justify-between px-6 pb-6">
        <div className="text-center">
          <h3 className="text-lg font-bold text-gray-900">ROOSTERS</h3>
        </div>
        <div className="text-center">
          <h3 className="text-lg font-bold text-gray-900">BRONCOS</h3>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex rounded-b-lg overflow-hidden">
        <Button
          className="flex-1 bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-3 px-4 rounded-none text-sm"
          variant="default"
        >
          COMPARE ODDS
        </Button>
        <Button
          className="flex-1 bg-orange-500 hover:bg-orange-600 text-white font-semibold py-3 px-4 rounded-none text-sm border-l border-r border-white/20"
          variant="default"
        >
          PLAY FANTASY
        </Button>
        <Button
          className="flex-1 bg-slate-800 hover:bg-slate-900 text-white font-semibold py-3 px-4 rounded-none text-sm"
          variant="default"
        >
          ENTER TIPS
        </Button>
      </div>
    </Card>
  )
}
