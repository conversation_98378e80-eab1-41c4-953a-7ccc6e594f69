import React from 'react'
interface Team {
  name: string
  logo: string
}
interface FixtureCardProps {
  homeTeam: Team
  awayTeam: Team
  time: string
  date: string
}
const FixtureCard: React.FC<FixtureCardProps> = ({
  homeTeam,
  awayTeam,
  time,
  date,
}) => {
  return (
    <div className="rounded-lg overflow-hidden shadow-lg bg-[#1e293b]">
      <div className="flex justify-between items-center p-4">
        {/* Home Team */}
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 mb-2">
            <img
              src={homeTeam.logo}
              alt={`${homeTeam.name} logo`}
              className="w-full h-full object-contain"
            />
          </div>
          <div className="text-white text-sm font-bold">{homeTeam.name}</div>
        </div>
        {/* Match Time */}
        <div className="flex flex-col items-center">
          <div className="text-white text-2xl font-bold">{time}</div>
          <div className="text-gray-400 text-xs">{date}</div>
        </div>
        {/* Away Team */}
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 mb-2">
            <img
              src={awayTeam.logo}
              alt={`${awayTeam.name} logo`}
              className="w-full h-full object-contain"
            />
          </div>
          <div className="text-white text-sm font-bold">{awayTeam.name}</div>
        </div>
      </div>
      {/* Action Buttons */}
      <div className="flex text-white text-xs font-bold">
        <button className="flex-1 py-2 bg-[#3b4fd7] hover:bg-[#2d3eb8] transition-colors text-center">
          COMPARE ODDS
        </button>
        <button className="flex-1 py-2 bg-[#f97316] hover:bg-[#ea6c10] transition-colors text-center">
          PLAY FANTASY
        </button>
        <button className="flex-1 py-2 bg-[#1e3a8a] hover:bg-[#15306f] transition-colors text-center">
          ENTER TIPS
        </button>
      </div>
    </div>
  )
}
export default FixtureCard