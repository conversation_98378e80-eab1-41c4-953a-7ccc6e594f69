import { StaticImageData } from 'next/image';
import { PUBLIC_IMAGES } from '@/lib/constants/publicImages';
import {
  AmericanFootball,
  Baseball,
  Boxing,
  Cricket,
  AustralianFootball,
  Basketball,
  EventsCalendar,
  Golf,
  IceHockey,
  MixedMartialArts,
  RugbyLeague,
  RugbyUnion,
  Soccer,
  Tennis,
} from '@/components/Icons/SportsIcons/index';
import HorseRacing from '@/components/Icons/SportsIcons/HorseRacing';
import HarnessRacing from '@/components/Icons/SportsIcons/HarnessRacing';
import GreyhoundRacing from '@/components/Icons/SportsIcons/GreyhoundRacing';
import { HeaderType } from '@/components/layouts/header';

type SubMenu = {
  label: string;
  href: string;
  logo?: () => JSX.Element;
};

export type HeaderMenu = {
  label: string;
  href: string;
  logo?: StaticImageData;
  subMenus?: SubMenu[];
  type?: HeaderType;
};

const HORSERACING_SUBMENUS: SubMenu[] = [
  {
    label: 'Horse Racing',
    href: '/horse-racing',
    logo: HorseRacing,
  },
  {
    label: 'Harness Racing',
    href: '/harness-racing',
    logo: HarnessRacing,
  },
  {
    label: 'Greyhound Racing',
    href: '/greyhound-racing',
    logo: GreyhoundRacing,
  },
];

const SPORTS_SUBMENUS: SubMenu[] = [
  {
    label: 'Australian Rules',
    href: '/sports/australian-rules',
    logo: AustralianFootball,
  },
  {
    label: 'American Football',
    href: '/sports/american-football',
    logo: AmericanFootball,
  },
  {
    label: 'Rugby League',
    href: '/sports/rugby-league',
    logo: RugbyLeague,
  },
  {
    label: 'Baseball',
    href: '/sports/baseball',
    logo: Baseball,
  },
  {
    label: 'Cricket',
    href: '/sports/cricket',
    logo: Cricket,
  },
  {
    label: 'Boxing',
    href: '/sports/boxing',
    logo: Boxing,
  },
  {
    label: 'Mixed Martial Arts',
    href: '/sports/mma',
    logo: MixedMartialArts,
  },
  {
    label: 'Golf',
    href: '/sports/golf',
    logo: Golf,
  },
  {
    label: 'Soccer',
    href: '/sports/soccer',
    logo: Soccer,
  },
  {
    label: 'Ice Hockey',
    href: '/sports/ice-hockey',
    logo: IceHockey,
  },
  {
    label: 'Basketball',
    href: '/sports/basketball',
    logo: Basketball,
  },
  {
    label: 'Rugby Union',
    href: '/sports/rugby-union',
    logo: RugbyUnion,
  },
  {
    label: 'Tennis',
    href: '/sports/tennis',
    logo: Tennis,
  },
  {
    label: 'Events Calendar',
    href: '/sports/events-calendar',
    logo: EventsCalendar,
  },
];

export const DEFAULT_HEADER_MENUS: HeaderMenu[] = [
  {
    label: 'Home',
    href: '/',
    type: 'default',
  },
  {
    label: 'Racing',
    href: '/racing',
    subMenus: HORSERACING_SUBMENUS,
  },
  {
    label: 'Sports',
    href: '/sports',
    subMenus: SPORTS_SUBMENUS,
  },
  {
    label: 'Podcast',
    href: '/podcast',
  },
  {
    label: 'Smart Info',
    href: '/smartinfo',
    logo: PUBLIC_IMAGES.SMARTINFOLOGO,
    type: 'smartInfo',
  },
  {
    label: 'SmartB Odds',
    href: '/smartb-odds',
    logo: PUBLIC_IMAGES.SMARTODDSLOGO,
    type: 'smartOdds',
  },
  {
    label: 'Smart Tipster',
    href: '/smart-tipster',
    logo: PUBLIC_IMAGES.SMARTTIPSTERLOGO,
    type: 'smartTipping',
  },
  {
    label: 'Smart Play',
    href: '/smartplay',
    logo: PUBLIC_IMAGES.SMARTPLAYLOGO,
    type: 'smartPlay',
  },
];

export const SMART_PLAY_HEADER_MENUS: HeaderMenu[] = [
  {
    label: 'Smart Play',
    href: '/smartplay',
    logo: PUBLIC_IMAGES.SMARTPLAYLOGO,
  },
  {
    label: 'All Comps',
    href: '/fantasy',
  },
  {
    label: 'Players',
    href: '/players',
  },
  {
    label: 'SmartPlay FAQs',
    href: '/smartplay-faqs',
  },
  {
    label: 'Rules & Scoring',
    href: '/rules-and-scoring',
  },
];

export const SMART_INFO_HEADER_MENUS: HeaderMenu[] = [
  {
    label: 'Smart Info',
    href: '/smartinfo',
    logo: PUBLIC_IMAGES.SMARTINFOLOGO,
  },
  {
    label: 'News',
    href: '/news',
  },
  {
    label: 'Saved Articles',
    href: '/saved-articles',
  },
  {
    label: 'Bookmakers',
    href: '/bookmakers',
  },
  {
    label: 'Our People',
    href: '/our-people',
  },
  {
    label: 'Recommended Websites',
    href: '/recommended-websites',
  },
];

export const SMART_ODDS_HEADER_MENUS: HeaderMenu[] = [
  {
    label: 'Smart Odds',
    href: '/smartb-odds',
    logo: PUBLIC_IMAGES.SMARTODDSLOGO,
  },
  {
    label: 'Racing',
    href: '/racing',
  },
  {
    label: 'Sports',
    href: '/sports',
  },
  {
    label: 'My BlackBook',
    href: '/my-blackbook',
  },
  {
    label: 'My SmartBook',
    href: '/my-tips',
  },
  {
    label: 'Feature Races',
    href: '/feature-races',
  },
  {
    label: 'Odds Status',
    href: '/odds-status',
  },
  {
    label: 'Contact Us',
    href: '/contact-us',
  },
];

export const SMART_TIPSTER_HEADER_MENUS: HeaderMenu[] = [
  {
    label: 'Smart Tipster',
    href: '/smart-tipster',
    logo: PUBLIC_IMAGES.SMARTTIPSTERLOGO,
  },
  {
    label: 'My Comps',
    href: '/my-comps',
  },
  {
    label: 'Join Comps',
    href: '/join-comps',
  },
  {
    label: 'Create Comp',
    href: '/create-comp',
  },
  {
    label: 'Rankings',
    href: '/rankings',
  },
  {
    label: 'Prizes',
    href: '/prizes',
  },
  {
    label: 'Terms & Conditions',
    href: '/terms-and-conditions',
  },
];
