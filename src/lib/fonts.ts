import localFont from 'next/font/local';

// TerminaTest Font
export const terminaTest = localFont({
  src: [
    {
      path: '../../public/fonts/TerminaTest-Thin.woff',
      weight: '100',
      style: 'normal',
    },
    {
      path: '../../public/fonts/TerminaTest-ExtraLight.woff',
      weight: '200',
      style: 'normal',
    },
    {
      path: '../../public/fonts/TerminaTest-Light.woff',
      weight: '300',
      style: 'normal',
    },
    {
      path: '../../public/fonts/TerminaTest-Regular.woff',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../../public/fonts/TerminaTest-Medium.woff',
      weight: '500',
      style: 'normal',
    },
    {
      path: '../../public/fonts/TerminaTest-Demi.woff',
      weight: '600',
      style: 'normal',
    },
    {
      path: '../../public/fonts/TerminaTest-Bold.woff',
      weight: '700',
      style: 'normal',
    },
    {
      path: '../../public/fonts/TerminaTest-Heavy.woff',
      weight: '800',
      style: 'normal',
    },
    {
      path: '../../public/fonts/TerminaTest-Black.woff',
      weight: '900',
      style: 'normal',
    },
  ],
  variable: '--font-termina-test',
  display: 'swap',
});

// Apotek Font Family
export const apotek = localFont({
  src: [
    // Regular Apotek
    {
      path: '../../public/fonts/Apotek_ExtraLight.otf',
      weight: '200',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_Light.otf',
      weight: '300',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_Regular.otf',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_Medium.otf',
      weight: '500',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_Semibold.otf',
      weight: '600',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_Bold.otf',
      weight: '700',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_Black.otf',
      weight: '900',
      style: 'normal',
    },
  ],
  variable: '--font-apotek',
  display: 'swap',
});

// Apotek Wide
export const apotekWide = localFont({
  src: [
    {
      path: '../../public/fonts/Apotek_Wide_ExtraLight.otf',
      weight: '200',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_Wide_Light.otf',
      weight: '300',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_Wide_Regular.otf',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_Wide_Medium.otf',
      weight: '500',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_Wide_Semibold.otf',
      weight: '600',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_Wide_Bold.otf',
      weight: '700',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_Wide_Black.otf',
      weight: '900',
      style: 'normal',
    },
  ],
  variable: '--font-apotek-wide',
  display: 'swap',
});

// Apotek ExtraWide
export const apotekExtraWide = localFont({
  src: [
    {
      path: '../../public/fonts/Apotek_ExtraWide_ExtraLight.otf',
      weight: '200',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_ExtraWide_Light.otf',
      weight: '300',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_ExtraWide_Regular.otf',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_ExtraWide_Medium.otf',
      weight: '500',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_ExtraWide_Semibold.otf',
      weight: '600',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_ExtraWide_Bold.otf',
      weight: '700',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_ExtraWide_Black.otf',
      weight: '900',
      style: 'normal',
    },
  ],
  variable: '--font-apotek-extrawide',
  display: 'swap',
});

// Apotek Condensed
export const apotekCond = localFont({
  src: [
    {
      path: '../../public/fonts/Apotek_Cond_ExtraLight.otf',
      weight: '200',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_Cond_Light.otf',
      weight: '300',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_Cond_Regular.otf',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_Cond_Medium.otf',
      weight: '500',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_Cond_Semibold.otf',
      weight: '600',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_Cond_Bold.otf',
      weight: '700',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_Cond_Black.otf',
      weight: '900',
      style: 'normal',
    },
  ],
  variable: '--font-apotek-cond',
  display: 'swap',
});

// Apotek ExtraCondensed
export const apotekExtraCond = localFont({
  src: [
    {
      path: '../../public/fonts/Apotek_ExtraCond_ExtraLight.otf',
      weight: '200',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_ExtraCond_Light.otf',
      weight: '300',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_ExtraCond_Regular.otf',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_ExtraCond_Medium.otf',
      weight: '500',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_ExtraCond_Semibold.otf',
      weight: '600',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_ExtraCond_Bold.otf',
      weight: '700',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_ExtraCond_Black.otf',
      weight: '900',
      style: 'normal',
    },
  ],
  variable: '--font-apotek-extracond',
  display: 'swap',
});

// Apotek Compressed
export const apotekComp = localFont({
  src: [
    {
      path: '../../public/fonts/Apotek_Comp_ExtraLight.otf',
      weight: '200',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_Comp_Light.otf',
      weight: '300',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_Comp_Regular.otf',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_Comp_Medium.otf',
      weight: '500',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_Comp_Semibold.otf',
      weight: '600',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_Comp_Bold.otf',
      weight: '700',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_Comp_Black.otf',
      weight: '900',
      style: 'normal',
    },
  ],
  variable: '--font-apotek-comp',
  display: 'swap',
});

// Apotek Extended
export const apotekExtended = localFont({
  src: [
    {
      path: '../../public/fonts/Apotek_Extended_ExtraLight.otf',
      weight: '200',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_Extended_Light.otf',
      weight: '300',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_Extended_Regular.otf',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_Extended_Medium.otf',
      weight: '500',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_Extended_Semibold.otf',
      weight: '600',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_Extended_Bold.otf',
      weight: '700',
      style: 'normal',
    },
    {
      path: '../../public/fonts/Apotek_Extended_Black.otf',
      weight: '900',
      style: 'normal',
    },
  ],
  variable: '--font-apotek-extended',
  display: 'swap',
}); 