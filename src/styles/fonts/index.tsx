// src/styles/font/index.ts
import { apotekComp, apotekCond, apotekExtended, apotekExtraCond, apotekExtraWide, apotekWide, terminaTest } from "../../lib/fonts";
import { apotek } from "../../lib/fonts";
import { variable } from "../variable";
export const fonts = {
  terminaTest,
  apotek,
  apotekWide,
  apotekExtraWide,
  apotekCond,
  apotekExtraCond,
  apotekComp,
  apotekExtended,
};


export function StyleGlobalFontVariable({font, fontVariable}: {font: string, fontVariable: string}) {
  return (
    <style>{`:root { ${fontVariable}: "${font}"; }`}</style>
  );
}