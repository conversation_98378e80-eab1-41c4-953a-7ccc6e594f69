/* Custom TerminaTest Fonts */
@font-face {
    font-family: 'TerminaTest';
    font-style: normal;
    font-weight: 100;
    src: url('/fonts/TerminaTest-Thin.woff') format('woff');
    font-display: swap;
  }
  
  @font-face {
    font-family: 'TerminaTest';
    font-style: normal;
    font-weight: 200;
    src: url('/fonts/TerminaTest-ExtraLight.woff') format('woff');
    font-display: swap;
  }
  
  @font-face {
    font-family: 'TerminaTest';
    font-style: normal;
    font-weight: 300;
    src: url('/fonts/TerminaTest-Light.woff') format('woff');
    font-display: swap;
  }
  
  @font-face {
    font-family: 'TerminaTest';
    font-style: normal;
    font-weight: 400;
    src: url('/fonts/TerminaTest-Regular.woff') format('woff');
    font-display: swap;
  }
  
  @font-face {
    font-family: 'TerminaTest';
    font-style: normal;
    font-weight: 500;
    src: url('/fonts/TerminaTest-Medium.woff') format('woff');
    font-display: swap;
  }
  
  @font-face {
    font-family: 'TerminaTest';
    font-style: normal;
    font-weight: 600;
    src: url('/fonts/TerminaTest-Demi.woff') format('woff');
    font-display: swap;
  }
  
  @font-face {
    font-family: 'TerminaTest';
    font-style: normal;
    font-weight: 700;
    src: url('/fonts/TerminaTest-Bold.woff') format('woff');
    font-display: swap;
  }
  
  @font-face {
    font-family: 'TerminaTest';
    font-style: normal;
    font-weight: 800;
    src: url('/fonts/TerminaTest-Heavy.woff') format('woff');
    font-display: swap;
  }
  
  @font-face {
    font-family: 'TerminaTest';
    font-style: normal;
    font-weight: 900;
    src: url('/fonts/TerminaTest-Black.woff') format('woff');
    font-display: swap;
  }
  
  /* Custom Apotek Fonts - Fallback declarations */
  @font-face {
    font-family: 'Apotek';
    font-style: normal;
    font-weight: 200;
    src: url('/fonts/Apotek_ExtraLight.otf') format('opentype');
    font-display: swap;
  }
  
  @font-face {
    font-family: 'Apotek';
    font-style: normal;
    font-weight: 300;
    src: url('/fonts/Apotek_Light.otf') format('opentype');
    font-display: swap;
  }
  
  @font-face {
    font-family: 'Apotek';
    font-style: normal;
    font-weight: 400;
    src: url('/fonts/Apotek_Regular.otf') format('opentype');
    font-display: swap;
  }
  
  @font-face {
    font-family: 'Apotek';
    font-style: normal;
    font-weight: 500;
    src: url('/fonts/Apotek_Medium.otf') format('opentype');
    font-display: swap;
  }
  
  @font-face {
    font-family: 'Apotek';
    font-style: normal;
    font-weight: 600;
    src: url('/fonts/Apotek_Semibold.otf') format('opentype');
    font-display: swap;
  }
  
  @font-face {
    font-family: 'Apotek';
    font-style: normal;
    font-weight: 700;
    src: url('/fonts/Apotek_Bold.otf') format('opentype');
    font-display: swap;
  }
  
  @font-face {
    font-family: 'Apotek';
    font-style: normal;
    font-weight: 900;
    src: url('/fonts/Apotek_Black.otf') format('opentype');
    font-display: swap;
  }
  