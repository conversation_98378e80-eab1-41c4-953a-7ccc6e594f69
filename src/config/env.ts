import * as z from 'zod';
import 'dotenv/config';

const createEnv = () => {
  const EnvSchema = z.object({
    API_URL: z.string().url(),
    FANTASY_API_URL: z.string().url(),
    FANTASY_API_BASE_URL: z.string().url(),
    MEDIA_URL: z.string().url(),
    COUNTRY_MEDIA_URL: z.string().url(),
    WP_BASE_URL: z.string().url(),
    VERSION: z.string(),
    RELEASE: z.string(),
    SUBSCRIPTION_USER_ID: z
      .string()
      .refine((val) => {
        try {
          const parsed = JSON.parse(val);
          return Array.isArray(parsed);
        } catch {
          return false;
        }
      }, {
        message: 'Must be a JSON stringified array',
      }),
    FACEBOOK_ID: z.string(),
    ENABLE_API_MOCKING: z
      .string()
      .refine((s) => s === 'true' || s === 'false')
      .transform((s) => s === 'true')
      .optional(),
    APP_URL: z.string().optional().default('http://localhost:3000'),
    APP_MOCK_API_PORT: z.string().optional().default('8080'),
  });

  const envVars = {
    API_URL: process.env.NEXT_PUBLIC_API_URL,
    FANTASY_API_URL: process.env.NEXT_PUBLIC_FANTASY_API_URL,
    FANTASY_API_BASE_URL: process.env.NEXT_PUBLIC_FANTASY_API_BASE_URL,
    MEDIA_URL: process.env.NEXT_PUBLIC_MEDIA_URL,
    COUNTRY_MEDIA_URL: process.env.NEXT_PUBLIC_COUNTRY_MEDIA_URL,
    WP_BASE_URL: process.env.NEXT_PUBLIC_WP_BASE_URL,
    VERSION: process.env.NEXT_PUBLIC_VERSION,
    RELEASE: process.env.NEXT_PUBLIC_RELEASE,
    SUBSCRIPTION_USER_ID: process.env.NEXT_PUBLIC_SUBSCRIPTION_USER_ID,
    FACEBOOK_ID: process.env.NEXT_PUBLIC_FACEBOOK_ID,
    ENABLE_API_MOCKING: process.env.NEXT_PUBLIC_ENABLE_API_MOCKING,
    APP_URL: process.env.NEXT_PUBLIC_URL,
    APP_MOCK_API_PORT: process.env.NEXT_PUBLIC_MOCK_API_PORT,
  };

  const parsedEnv = EnvSchema.safeParse(envVars);

  if (!parsedEnv.success) {
    throw new Error(
      `Invalid env provided.
  The following variables are missing or invalid:
  ${Object.entries(parsedEnv.error.flatten().fieldErrors)
    .map(([k, v]) => `- ${k}: ${v}`)
    .join('\n')}
  `
    );
  }

  return parsedEnv.data;
};

export const env = createEnv();
