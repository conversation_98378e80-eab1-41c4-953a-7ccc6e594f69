import {
  dehydrate,
  HydrationBoundary,
  QueryClient,
} from '@tanstack/react-query';
import { ReactNode } from 'react';

import { AppProvider } from '@/app/provider';
import { getUserQueryOptions } from '@/lib/auth';
import {   apotekComp, apotekExtraCond, apotekCond, apotekExtraWide, apotekWide, apotek, terminaTest, apotekExtended   } from '@/lib/fonts';

import '@/styles/globals.css';
import '@/styles/font.css';
import Header from '@/components/layouts/header';
import HeaderEvents from '@/components/layouts/header/events';

export const metadata = {
  title: 'Bulletproof React',
  description: 'Showcasing Best Practices For Building React Applications',
};

const RootLayout = async ({ children }: { children: ReactNode }) => {
  const queryClient = new QueryClient();

  await queryClient.prefetchQuery(getUserQueryOptions());

  const dehydratedState = dehydrate(queryClient);

  return (
    <html lang="en">
      <body className={`${terminaTest.variable} ${apotek.variable} ${apotekWide.variable} ${apotekExtraWide.variable} ${apotekCond.variable} ${apotekExtraCond.variable} ${apotekComp.variable} ${apotekExtended.variable}`}>
        <AppProvider>
          <HydrationBoundary state={dehydratedState}>
            <HeaderEvents />
            <Header  />
            {children}
          </HydrationBoundary>
        </AppProvider>
      </body>
    </html>
  );
};

export default RootLayout;

// We are not prerendering anything because the app is highly dynamic
// and the data depends on the user so we need to send cookies with each request
export const dynamic = 'force-dynamic';
