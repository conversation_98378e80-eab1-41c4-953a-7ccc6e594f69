import NewsSection from '@/components/shared/News';
import ProductCards from '@/components/shared/product-card/product-card';
import { TipOfTheDayComponent, TipOfTheDayMobileComponent } from '@/components/shared/Tips';
import { checkLoggedIn } from '@/utils/auth';

const HomePage = () => {
  const isLoggedIn = checkLoggedIn();

  return (
    <div>
      <ProductCards />
      <NewsSection />
      <TipOfTheDayMobileComponent />
      <TipOfTheDayComponent />
    </div>
  );  
};

export default HomePage;
