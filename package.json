{"name": "bulletproof-react-nextjs-pages", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest", "test-e2e": "pm2 start \"yarn run-mock-server\" --name server && yarn playwright test", "prepare": "husky", "check-types": "tsc --project tsconfig.json --pretty --noEmit", "generate": "plop", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "run-mock-server": "tsx ./mock-server.ts"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@next/env": "^14.2.5", "@ngneat/falso": "^7.2.0", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.0.3", "@tanstack/react-query": "^5.32.0", "@tanstack/react-query-devtools": "^5.32.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.11", "embla-carousel-react": "^8.6.0", "eslint-plugin-check-file": "^2.8.0", "isomorphic-dompurify": "^2.14.0", "lucide-react": "^0.525.0", "marked": "^12.0.2", "nanoid": "^5.0.7", "next": "^14.2.5", "react": "^18.3.1", "react-dom": "^18.3.1", "react-error-boundary": "^4.0.13", "react-hook-form": "^7.51.3", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.4", "zustand": "^4.5.2"}, "devDependencies": {"@eslint/eslintrc": "^3.0.2", "@mswjs/data": "^0.16.1", "@mswjs/http-middleware": "^0.10.1", "@playwright/test": "^1.43.1", "@storybook/addon-a11y": "^8.0.10", "@storybook/addon-actions": "^8.0.9", "@storybook/addon-essentials": "^8.0.9", "@storybook/addon-links": "^8.0.9", "@storybook/nextjs": "^8.2.9", "@storybook/node-logger": "^8.0.9", "@storybook/react": "^8.0.9", "@tailwindcss/typography": "^0.5.13", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^15.0.5", "@testing-library/user-event": "^14.5.2", "@types/cors": "^2.8.17", "@types/dompurify": "^3.0.5", "@types/js-cookie": "^3.0.6", "@types/marked": "^6.0.0", "@types/node": "^20.12.7", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.8.0", "@typescript-eslint/parser": "^7.8.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "cors": "^2.8.5", "dotenv": "^16.4.5", "eslint": "8", "eslint-config-next": "^14.2.5", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jest-dom": "^5.4.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-playwright": "^1.6.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-tailwindcss": "^3.15.1", "eslint-plugin-testing-library": "^6.2.2", "eslint-plugin-vitest": "^0.5.4", "express": "^4.19.2", "husky": "^9.0.11", "jest-environment-jsdom": "^29.7.0", "js-cookie": "^3.0.5", "jsdom": "^24.0.0", "lint-staged": "^15.2.2", "msw": "^2.2.14", "pino-http": "^10.1.0", "pino-pretty": "^11.1.0", "plop": "^4.0.1", "pm2": "^5.4.0", "postcss": "^8.4.38", "prettier": "^3.2.5", "storybook": "^8.0.9", "tailwindcss": "^3.4.3", "tsx": "^4.17.0", "typescript": "^5.4.5", "vite-tsconfig-paths": "^4.3.2", "vitest": "^2.1.4"}, "msw": {"workerDirectory": "public"}}