/** @type {import('tailwindcss').Config} */

const defaultTheme = require('tailwindcss/defaultTheme');

module.exports = {
    darkMode: ['class'],
    content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
  	container: {
  		center: true,
  		padding: '2rem',
  		screens: {
  			'2xl': '1400px'
  		}
  	},
  	extend: {
  		fontFamily: {
  			sans: [
  				'Inter var',
                    ...defaultTheme.fontFamily.sans
                ],
  			termina: [
  				'var(--font-termina-test)',
  				'sans-serif'
  			],
  			apotek: [
  				'var(--font-apotek)',
  				'sans-serif'
  			],
  			'apotek-wide': [
  				'var(--font-apotek-wide)',
  				'sans-serif'
  			],
  			'apotek-extrawide': [
  				'var(--font-apotek-extrawide)',
  				'sans-serif'
  			],
  			'apotek-cond': [
  				'var(--font-apotek-cond)',
  				'sans-serif'
  			],
  			'apotek-extracond': [
  				'var(--font-apotek-extracond)',
  				'sans-serif'
  			],
  			'apotek-comp': [
  				'var(--font-apotek-comp)',
  				'sans-serif'
  			],
  			'apotek-extended': [
  				'var(--font-apotek-extended)',
  				'sans-serif'
  			]
  		},
  		fontSize: {
  			'headline-1': '81px',
  			'headline-2': '52px',
        'headline-mobile-1': '70px',
        'headline-mobile-2': '40px',

  		},
  		backgroundImage: {
  			'primary-gradient': 'linear-gradient(to bottom, #4455C7, #181C3D)',
  			'secondary-gradient': 'linear-gradient(to bottom, #FC7914, #FC4714)',
  			'dark-primary': 'linear-gradient(to bottom, #003764, #090B0D)'
  		},
  		colors: {
  			border: 'hsl(var(--border))',
			secondary: '#FC4714',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			primaryborder: '#939BD2',
			primarybutton: '#4455C7',
  			secondaryborder: '#FDA289',
  			darkprimaryborder: '#7F9AB1',
			textprimary: '#333333',
  			primary: {
  				DEFAULT: 'hsl(var(--primary))',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			secondary: {
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			chart: {
  				'1': 'hsl(var(--chart-1))',
  				'2': 'hsl(var(--chart-2))',
  				'3': 'hsl(var(--chart-3))',
  				'4': 'hsl(var(--chart-4))',
  				'5': 'hsl(var(--chart-5))'
  			}
  		},
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		},
  		keyframes: {
  			'accordion-down': {
  				from: {
  					height: '0'
  				},
  				to: {
  					height: 'var(--radix-accordion-content-height)'
  				}
  			},
  			'accordion-up': {
  				from: {
  					height: 'var(--radix-accordion-content-height)'
  				},
  				to: {
  					height: '0'
  				}
  			}
  		},
  		animation: {
  			'accordion-down': 'accordion-down 0.2s ease-out',
  			'accordion-up': 'accordion-up 0.2s ease-out'
  		},
      stroke: {
        'secondary-200': '#FDA289',
      },
  	}
  },
  plugins: [require('tailwindcss-animate'), require('@tailwindcss/typography')],
};
