/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  images: {
    dangerouslyAllowSVG: true,
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'media.staging.smartb.au.sydney.digiground.com.au',
        port: '',
        pathname: '**',
      },
      {
        protocol: 'http',
        hostname: 'media.smartb.com.au',
        port: '',
        pathname: '**',
      },
    ],
    domains: [
      'media.staging.smartb.au.sydney.digiground.com.au',
      'media.smartb.com.au',
    ],
  },
};

export default nextConfig;
